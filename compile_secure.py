#!/usr/bin/env python3
"""
Secure compilation script for Apple ID Bot
"""

import os
import shutil
import subprocess
import sys
from pathlib import Path

def clean_build_dirs():
    """پاک کردن دایرکتوری‌های build قبلی"""
    dirs_to_clean = ['build', 'dist', '__pycache__', 'obfuscated_code']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ Cleaned {dir_name}")

def obfuscate_code():
    """Obfuscate کردن کد با PyArmor"""
    print("🔒 Starting code obfuscation...")
    
    # ایجاد پروژه PyArmor
    subprocess.run([
        'pyarmor', 'gen', 
        '--output', 'obfuscated_code',
        '--recursive',
        '--exclude', 'backups',
        '--exclude', 'config', 
        '--exclude', 'database',
        '--exclude', '.env',
        '--exclude', 'test_*.py',
        '--exclude', '*.log',
        '.'
    ], check=True)
    
    print("✅ Code obfuscation completed")

def build_executable():
    """ساخت فایل اجرایی"""
    print("🔨 Building executable...")
    
    # استفاده از کد obfuscated شده
    os.chdir('obfuscated_code')
    
    subprocess.run([
        'pyinstaller',
        '--onefile',
        '--noconsole',  # حذف کنید اگر می‌خواهید console ببینید
        '--hidden-import=telegram',
        '--hidden-import=telegram.ext',
        '--hidden-import=aiosqlite',
        '--hidden-import=openpyxl',
        '--add-data=messages.py:.',
        '--add-data=constants.py:.',
        '--distpath=../dist',
        '--workpath=../build',
        'bot.py'
    ], check=True)
    
    os.chdir('..')
    print("✅ Executable built successfully")

def create_install_structure():
    """ایجاد ساختار نصب"""
    print("📦 Creating installation structure...")
    
    # ایجاد پوشه نهایی
    install_dir = 'apple_id_bot_release'
    if os.path.exists(install_dir):
        shutil.rmtree(install_dir)
    
    os.makedirs(install_dir)
    os.makedirs(f'{install_dir}/backups')
    os.makedirs(f'{install_dir}/config')
    os.makedirs(f'{install_dir}/database')
    
    # کپی فایل اجرایی
    shutil.copy('dist/apple_id_bot', f'{install_dir}/')
    
    # کپی فایل‌های ضروری
    essential_files = [
        '.env.example',
        'requirements.txt',
        'README.md'
    ]
    
    for file in essential_files:
        if os.path.exists(file):
            shutil.copy(file, f'{install_dir}/')
    
    # ایجاد install.sh
    create_install_script(install_dir)
    
    print(f"✅ Installation package created in {install_dir}/")

def create_install_script(install_dir):
    """ایجاد اسکریپت نصب"""
    install_script = f"""#!/bin/bash

# Apple ID Bot Installation Script
# Auto-generated secure installation

set -e

echo "🚀 Installing Apple ID Bot..."

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo "❌ This script should not be run as root for security reasons"
   exit 1
fi

# Create bot directory
BOT_DIR="$HOME/apple_id_bot"
mkdir -p "$BOT_DIR"

# Copy files
echo "📁 Copying bot files..."
cp apple_id_bot "$BOT_DIR/"
cp -r backups "$BOT_DIR/"
cp -r config "$BOT_DIR/"
cp -r database "$BOT_DIR/"

# Set permissions
chmod +x "$BOT_DIR/apple_id_bot"

# Create .env file if not exists
if [ ! -f "$BOT_DIR/.env" ]; then
    echo "⚙️  Creating .env file..."
    cp .env.example "$BOT_DIR/.env"
    echo "📝 Please edit $BOT_DIR/.env with your bot configuration"
fi

# Create systemd service
echo "🔧 Creating systemd service..."
sudo tee /etc/systemd/system/apple-id-bot.service > /dev/null <<EOF
[Unit]
Description=Apple ID Bot
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$BOT_DIR
ExecStart=$BOT_DIR/apple_id_bot
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable apple-id-bot.service

echo "✅ Installation completed!"
echo "📍 Bot installed in: $BOT_DIR"
echo "⚙️  Edit configuration: $BOT_DIR/.env"
echo "🚀 Start bot: sudo systemctl start apple-id-bot"
echo "📊 Check status: sudo systemctl status apple-id-bot"
echo "📋 View logs: sudo journalctl -u apple-id-bot -f"
"""
    
    with open(f'{install_dir}/install.sh', 'w') as f:
        f.write(install_script)
    
    os.chmod(f'{install_dir}/install.sh', 0o755)

def main():
    """تابع اصلی"""
    print("🔐 Apple ID Bot Secure Compilation")
    print("=" * 50)
    
    try:
        # مرحله 1: پاک کردن فایل‌های قبلی
        clean_build_dirs()
        
        # مرحله 2: Obfuscate کردن کد
        obfuscate_code()
        
        # مرحله 3: ساخت فایل اجرایی
        build_executable()
        
        # مرحله 4: ایجاد بسته نصب
        create_install_structure()
        
        print("\n🎉 Compilation completed successfully!")
        print("📦 Installation package: apple_id_bot_release/")
        print("🚀 Upload to server and run: ./install.sh")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {{e}}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {{e}}")
        sys.exit(1)

if __name__ == "__main__":
    main()