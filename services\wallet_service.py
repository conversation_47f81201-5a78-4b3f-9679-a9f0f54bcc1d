from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
import os
from dotenv import load_dotenv

from logger import logger, bot_logger
from error_handler import error_handler
from models.wallet import wallet_model
from messages import *
from services.settings_service import settings_service

# Load environment variables
load_dotenv()

class WalletService:
    def __init__(self):
        self.payment_group_id = os.getenv('PAYMENT_GROUP_ID')
        if not self.payment_group_id:
            logger.warning("PAYMENT_GROUP_ID not configured in .env")
        else:
            logger.info(f"WalletService initialized with payment group: {self.payment_group_id}")
        logger.info("WalletService initialized successfully")
    
    async def show_wallet_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show wallet menu to user"""
        try:
            user_id = update.effective_user.id
            logger.debug(f"User {user_id} requested wallet menu")

            # Get user balance
            balance = await wallet_model.get_user_balance(user_id)
            
            # Get recent transactions
            transactions = await wallet_model.get_user_transactions(user_id, limit=5)
            
            # Get dynamic button text and icon
            wallet_text = settings_service.get_button_text("wallet")
            wallet_icon = wallet_text.split()[0] if wallet_text else "💳"

            message = f"{wallet_icon} {WALLET_HEADER}"
            message += CURRENT_BALANCE.format(balance=balance)

            if transactions:
                message += RECENT_TRANSACTIONS
                for transaction in transactions:
                    date = transaction['created_at'][:10]
                    amount = transaction['amount']
                    status = transaction['status']
                    
                    status_emoji = {
                        'pending': '⏳',
                        'approved': '✅',
                        'rejected': '❌',
                        'completed': '✅'
                    }.get(status, '❓')
                    
                    if transaction['transaction_type'] == 'deposit':
                        transaction_type = 'واریز'
                    elif transaction['transaction_type'] == 'purchase':
                        transaction_type = '🛒 خرید'
                    else:
                        transaction_type = 'خرید'
                    
                    message += f"• {status_emoji} {transaction_type}: {amount:,} تومان ({date})\n"
                
                message += "\n"
            
            keyboard = [
                [InlineKeyboardButton(BTN_DEPOSIT, callback_data="wallet_deposit")],
                [InlineKeyboardButton(BTN_HISTORY, callback_data="wallet_history")]
            ]
            
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            if update.callback_query:
                await update.callback_query.edit_message_text(
                    message, parse_mode='Markdown', reply_markup=reply_markup
                )
            else:
                await update.message.reply_text(
                    message, parse_mode='Markdown', reply_markup=reply_markup
                )
            
            bot_logger.log_user_action(user_id, update.effective_user.username or "N/A", "VIEW_WALLET", f"Balance: {balance}")
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_wallet_menu")
    
    async def start_deposit_process(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Start deposit process - ask for amount"""
        try:
            message = WALLET_DEPOSIT_HEADER + DEPOSIT_AMOUNT_PROMPT + "\n\n❌ **برای لغو:** دستور `/cancel` را ارسال کنید"
            
            # Set user state for next message
            context.user_data['waiting_for_deposit_amount'] = True

            keyboard = [
                [InlineKeyboardButton("🔙 بازگشت", callback_data="wallet_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            if update.callback_query:
                await update.callback_query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)
            else:
                await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)
            
            bot_logger.log_user_action(
                update.effective_user.id, 
                update.effective_user.username or "N/A", 
                "START_DEPOSIT", 
                "Deposit process started"
            )
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "start_deposit_process")
    
    async def process_deposit_amount(self, update: Update, context: ContextTypes.DEFAULT_TYPE, amount_text: str) -> None:
        """Process deposit amount and ask for receipt"""
        try:
            user_id = update.effective_user.id
            logger.debug(f"User {user_id} entered deposit amount: {amount_text}")

            # Validate amount
            try:
                amount = int(amount_text.replace(',', '').replace('.', ''))
            except ValueError:
                logger.warning(f"User {user_id} entered invalid amount: {amount_text}")
                await update.message.reply_text(INVALID_AMOUNT)
                return

            if amount < 10000:
                logger.warning(f"User {user_id} entered amount too low: {amount}")
                await update.message.reply_text(AMOUNT_TOO_LOW)
                return

            if amount > 10000000:
                logger.warning(f"User {user_id} entered amount too high: {amount}")
                await update.message.reply_text(AMOUNT_TOO_HIGH)
                return
            
            # Store amount in user data
            context.user_data['deposit_amount'] = amount
            context.user_data['waiting_for_deposit_amount'] = False
            context.user_data['waiting_for_receipt'] = True

            logger.info(f"User {user_id} set deposit amount: {amount:,} تومان")
            
            message = DEPOSIT_CONFIRMATION.format(amount=amount) + "\n\n❌ **برای لغو:** دستور `/cancel` را ارسال کنید"
            
            # No keyboard needed - user can use /cancel command
            reply_markup = None
            
            await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)
            
            bot_logger.log_user_action(user_id, update.effective_user.username or "N/A", "DEPOSIT_AMOUNT", f"Amount: {amount}")
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "process_deposit_amount")
    
    async def process_receipt(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Process receipt image and send to payment group"""
        try:
            user_id = update.effective_user.id
            user = update.effective_user
            amount = context.user_data.get('deposit_amount')
            
            if not amount:
                await update.message.reply_text(RECEIPT_PROCESSING_ERROR)
                return

            # Check if message has photo
            if not update.message.photo:
                await update.message.reply_text(SEND_RECEIPT_IMAGE)
                return
            
            # Create deposit request in database
            transaction_id = await wallet_model.create_deposit_request(
                user_id, amount, update.message.message_id
            )
            
            if not transaction_id:
                await update.message.reply_text(RECEIPT_PROCESSING_ERROR)
                return
            
            # Send receipt to payment group for admin review
            receipt_sent = False
            if self.payment_group_id:
                try:
                    await self.send_receipt_to_group(update, context, transaction_id, user, amount)
                    receipt_sent = True
                    logger.info(f"Receipt successfully sent to payment group for transaction {transaction_id}")
                except Exception as e:
                    logger.error(f"Failed to send receipt to payment group for transaction {transaction_id}: {str(e)}")
                    bot_logger.log_error(e, context=f"Send receipt to group - Transaction {transaction_id}")
                    # Don't fail the whole process, but log the error
            else:
                logger.warning("PAYMENT_GROUP_ID not configured - receipt not sent to group")

            # Clear user state
            context.user_data.clear()

            if receipt_sent:
                message = RECEIPT_RECEIVED.format(amount=amount, transaction_id=transaction_id)
            else:
                message = f"""📋 **رسید دریافت شد**

💵 **مبلغ:** {amount:,} تومان
🆔 **شماره تراکنش:** {transaction_id}

⚠️ **توجه:** رسید شما ثبت شد اما به دلیل مشکل فنی به گروه ادمین ارسال نشد.
لطفاً با پشتیبانی تماس بگیرید.

⏰ زمان بررسی: حداکثر 24 ساعت"""

            await update.message.reply_text(message, parse_mode='Markdown')
            
            logger.info(f"Receipt processed for user {user_id}: {amount} Toman")
            bot_logger.log_user_action(user_id, user.username or "N/A", "RECEIPT_SENT", f"Transaction {transaction_id} - {amount} Toman")
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "process_receipt")
    
    async def send_receipt_to_group(self, update: Update, context: ContextTypes.DEFAULT_TYPE, transaction_id: int, user, amount: int) -> None:
        """Send receipt to payment group for admin review"""
        try:
            logger.info(f"Attempting to send receipt to payment group {self.payment_group_id} for transaction {transaction_id}")

            user_info = f"👤 **کاربر:** {user.first_name or ''} {user.last_name or ''}".strip()
            if user.username:
                user_info += f" (@{user.username})"
            user_info += f"\n🆔 **ID:** `{user.id}`"

            message = PAYMENT_GROUP_DEPOSIT_REQUEST.format(
                user_info=user_info,
                amount=amount,
                transaction_id=transaction_id,
                date=update.message.date.strftime('%Y/%m/%d %H:%M')
            )

            keyboard = [
                [
                    InlineKeyboardButton(BTN_APPROVE, callback_data=f"approve_deposit_{transaction_id}"),
                    InlineKeyboardButton(BTN_REJECT, callback_data=f"reject_deposit_{transaction_id}")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Forward the photo to payment group
            try:
                forward_result = await context.bot.forward_message(
                    chat_id=self.payment_group_id,
                    from_chat_id=update.effective_chat.id,
                    message_id=update.message.message_id
                )
                logger.info(f"Photo forwarded successfully to payment group. Message ID: {forward_result.message_id}")
            except Exception as forward_error:
                logger.error(f"Failed to forward photo to payment group: {str(forward_error)}")
                raise forward_error

            # Send the review message
            try:
                review_result = await context.bot.send_message(
                    chat_id=self.payment_group_id,
                    text=message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                logger.info(f"Review message sent successfully to payment group. Message ID: {review_result.message_id}")
            except Exception as message_error:
                logger.error(f"Failed to send review message to payment group: {str(message_error)}")
                raise message_error

            logger.info(f"Receipt sent to payment group for transaction {transaction_id}")
            bot_logger.log_admin_action(0, "RECEIPT_SENT_TO_GROUP", f"Transaction {transaction_id} sent to payment group")

        except Exception as e:
            logger.error(f"Failed to send receipt to group: {str(e)}")
            logger.error(f"Payment group ID: {self.payment_group_id}")
            logger.error(f"User ID: {user.id}")
            logger.error(f"Transaction ID: {transaction_id}")
            bot_logger.log_error(e, context=f"Send receipt to group - Transaction {transaction_id}")
            raise e
    
    async def show_transaction_history(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show user transaction history"""
        try:
            user_id = update.effective_user.id
            
            transactions = await wallet_model.get_user_transactions(user_id, limit=20)
            
            if not transactions:
                message = TRANSACTION_HISTORY_HEADER + NO_TRANSACTIONS
            else:
                message = TRANSACTION_HISTORY_HEADER
                
                for transaction in transactions:
                    date = transaction['created_at'][:16].replace('T', ' ')
                    amount = transaction['amount']
                    status = transaction['status']
                    transaction_type = transaction['transaction_type']
                    
                    status_emoji = {
                        'pending': '⏳',
                        'approved': '✅',
                        'rejected': '❌',
                        'completed': '✅'
                    }.get(status, '❓')
                    
                    type_text = {
                        'deposit': 'واریز',
                        'purchase': 'خرید'
                    }.get(transaction_type, transaction_type)
                    
                    message += f"{status_emoji} **{type_text}**\n"
                    message += f"💰 مبلغ: {amount:,} تومان\n"
                    message += f"📅 تاریخ: {date}\n"
                    if transaction.get('description'):
                        message += f"📝 توضیحات: {transaction['description']}\n"
                    message += "\n"
            
            keyboard = [[InlineKeyboardButton(f"{BTN_BACK} به کیف پول", callback_data="wallet_menu")]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.callback_query.edit_message_text(
                message, parse_mode='Markdown', reply_markup=reply_markup
            )
            
            bot_logger.log_user_action(user_id, update.effective_user.username or "N/A", "VIEW_HISTORY", f"Viewed {len(transactions)} transactions")
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_transaction_history")

# Create global instance
wallet_service = WalletService()
