from telegram import Update, KeyboardButton, ReplyKeyboardMarkup
from telegram.ext import ContextTypes
from logger import bot_logger, logger
from error_handler import error_handler
from utils.auth import AuthUtils


class AdminMenuService:
    def __init__(self):
        self.auth_utils = AuthUtils()
        logger.info("AdminMenuService initialized successfully")

    async def show_main_admin_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show main admin menu with 6 main categories"""
        try:
            user_id = update.effective_user.id
            
            # Only admins can access admin menu
            if not await self.auth_utils.is_admin(user_id):
                await update.message.reply_text("❌ شما دسترسی به پنل ادمین ندارید")
                return

            # Clear any menu state
            context.user_data.pop('current_menu', None)
            context.user_data['current_menu'] = 'main'

            # Base keyboard for all admins
            keyboard = [
                [KeyboardButton("📊 آمار و گزارشات"), KeyboardButton("🗄️ مدیریت داده‌ها")],
                [KeyboardButton("💰 مدیریت مالی"), KeyboardButton("📢 ارتباطات")],
                [KeyboardButton("⚙️ تنظیمات سیستم")]
            ]

            # Add master admin only menu
            if self.auth_utils.is_master_admin(user_id):
                keyboard.insert(1, [KeyboardButton("👥 مدیریت کاربران")])

            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
            
            message = """🏠 **منوی اصلی ادمین**

خوش آمدید! لطفاً بخش مورد نظر خود را انتخاب کنید:

📊 **آمار و گزارشات** - مشاهده آمار کامل ربات و سرور
🗄️ **مدیریت داده‌ها** - مدیریت Apple ID ها و دیتابیس
👥 **مدیریت کاربران** - جست‌وجو و مدیریت کاربران (ادمین اصلی)
💰 **مدیریت مالی** - تراکنش‌ها، پلن‌ها و قیمت‌ها
📢 **ارتباطات** - پیام‌رسانی و کانال‌های جوین اجباری
⚙️ **تنظیمات سیستم** - تنظیمات عمومی و امنیتی"""

            # Check if this is from callback query or message
            if hasattr(update, 'callback_query') and update.callback_query:
                await update.callback_query.edit_message_text(
                    message,
                    parse_mode='Markdown'
                )
                # Also update keyboard
                await update.callback_query.message.reply_text(
                    "🔄 منوی اصلی بروزرسانی شد",
                    reply_markup=reply_markup
                )
            else:
                await update.message.reply_text(
                    message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

            bot_logger.log_admin_action(user_id, "VIEW_MAIN_ADMIN_MENU", "Viewed main admin menu")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_main_admin_menu")

    async def show_stats_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show stats and reports submenu"""
        try:
            user_id = update.effective_user.id
            
            if not await self.auth_utils.is_admin(user_id):
                await update.message.reply_text("❌ شما دسترسی به این بخش ندارید")
                return

            context.user_data['current_menu'] = 'stats'

            keyboard = [
                [KeyboardButton("📈 آمار کامل ربات"), KeyboardButton("📉 تاریخچه آمار")],
                [KeyboardButton("🖥️ آمار سرور")],
                [KeyboardButton("🔙 بازگشت به منوی اصلی")]
            ]

            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
            
            message = """📊 **آمار و گزارشات**

📈 **آمار کامل ربات** - مشاهده آمار جامع ربات
📉 **تاریخچه آمار** - بررسی روند آمار در طول زمان
🖥️ **آمار سرور** - اطلاعات سیستم و منابع سرور

🔙 برای بازگشت به منوی اصلی، دکمه بازگشت را بزنید."""

            await update.message.reply_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            bot_logger.log_admin_action(user_id, "VIEW_STATS_MENU", "Viewed stats menu")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_stats_menu")

    async def show_data_management_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show data management submenu"""
        try:
            user_id = update.effective_user.id
            
            if not await self.auth_utils.is_admin(user_id):
                await update.message.reply_text("❌ شما دسترسی به این بخش ندارید")
                return

            context.user_data['current_menu'] = 'data'

            keyboard = [
                [KeyboardButton("📱 مدیریت Apple ID"), KeyboardButton("🗃️ مدیریت دیتابیس")],
                [KeyboardButton("📱 نمایش اکانت‌ها")],
                [KeyboardButton("🔙 بازگشت به منوی اصلی")]
            ]

            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
            
            message = """🗄️ **مدیریت داده‌ها**

📱 **مدیریت Apple ID** - مشاهده و مدیریت اکانت‌های Apple ID
🗃️ **مدیریت دیتابیس** - آپلود، دانلود و مدیریت فایل‌های Excel
📱 **نمایش اکانت‌ها** - مشاهده لیست اکانت‌ها از دیتابیس

🔙 برای بازگشت به منوی اصلی، دکمه بازگشت را بزنید."""

            await update.message.reply_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            bot_logger.log_admin_action(user_id, "VIEW_DATA_MENU", "Viewed data management menu")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_data_management_menu")

    async def show_user_management_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show user management submenu (master admin only)"""
        try:
            user_id = update.effective_user.id
            
            # Only master admin can access user management
            if not self.auth_utils.is_master_admin(user_id):
                await update.message.reply_text("❌ فقط ادمین اصلی می‌تواند کاربران را مدیریت کند")
                return

            context.user_data['current_menu'] = 'users'

            keyboard = [
                [KeyboardButton("🔍 جست و جوی کاربر"), KeyboardButton("👑 مدیریت ادمین‌ها")],
                [KeyboardButton("📋 لیست کاربران"), KeyboardButton("📊 آمار تفصیلی کاربران")],
                [KeyboardButton("🔙 بازگشت به منوی اصلی")]
            ]

            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
            
            message = """👥 **مدیریت کاربران**

🔍 **جست و جوی کاربر** - جست‌وجو و مدیریت کاربر خاص
👑 **مدیریت ادمین‌ها** - اضافه/حذف ادمین‌ها
📋 **لیست کاربران** - مشاهده لیست تمام کاربران
📊 **آمار تفصیلی کاربران** - آمار کامل کاربران

🔙 برای بازگشت به منوی اصلی، دکمه بازگشت را بزنید."""

            await update.message.reply_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            bot_logger.log_admin_action(user_id, "VIEW_USER_MGMT_MENU", "Viewed user management menu")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_user_management_menu")

    async def show_financial_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show financial management submenu"""
        try:
            user_id = update.effective_user.id
            
            if not await self.auth_utils.is_admin(user_id):
                await update.message.reply_text("❌ شما دسترسی به این بخش ندارید")
                return

            context.user_data['current_menu'] = 'financial'

            keyboard = [
                [KeyboardButton("💳 تراکنش‌های در انتظار"), KeyboardButton("📦 مدیریت پلن‌ها")],
                [KeyboardButton("💸 مدیریت قیمت دامنه‌ها")],
                [KeyboardButton("🔙 بازگشت به منوی اصلی")]
            ]

            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
            
            message = """💰 **مدیریت مالی**

💳 **تراکنش‌های در انتظار** - بررسی و تایید واریزها
📦 **مدیریت پلن‌ها** - مدیریت پلن‌های خرید
💸 **مدیریت قیمت دامنه‌ها** - تنظیم قیمت دامنه‌های مختلف

🔙 برای بازگشت به منوی اصلی، دکمه بازگشت را بزنید."""

            await update.message.reply_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            bot_logger.log_admin_action(user_id, "VIEW_FINANCIAL_MENU", "Viewed financial menu")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_financial_menu")

    async def show_communications_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show communications submenu"""
        try:
            user_id = update.effective_user.id

            if not await self.auth_utils.is_admin(user_id):
                await update.message.reply_text("❌ شما دسترسی به این بخش ندارید")
                return

            context.user_data['current_menu'] = 'communications'

            keyboard = [
                [KeyboardButton("📨 ارسال پیام به همه")],
                [KeyboardButton("📺 مدیریت کانال‌های جوین اجباری")],
                [KeyboardButton("🔙 بازگشت به منوی اصلی")]
            ]

            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

            message = """📢 **ارتباطات**

📨 **ارسال پیام به همه** - ارسال پیام همگانی به کاربران
📺 **مدیریت کانال‌های جوین اجباری** - مدیریت کانال‌های الزامی

🔙 برای بازگشت به منوی اصلی، دکمه بازگشت را بزنید."""

            await update.message.reply_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            bot_logger.log_admin_action(user_id, "VIEW_COMMUNICATIONS_MENU", "Viewed communications menu")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_communications_menu")

    async def show_system_settings_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show system settings submenu"""
        try:
            user_id = update.effective_user.id

            if not await self.auth_utils.is_admin(user_id):
                await update.message.reply_text("❌ شما دسترسی به این بخش ندارید")
                return

            context.user_data['current_menu'] = 'settings'

            keyboard = [
                [KeyboardButton("🔧 تنظیمات عمومی")],
                [KeyboardButton("🔙 بازگشت به منوی اصلی")]
            ]

            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

            message = """⚙️ **تنظیمات سیستم**

🔧 **تنظیمات عمومی** - تنظیمات کلی ربات

🔙 برای بازگشت به منوی اصلی، دکمه بازگشت را بزنید."""

            await update.message.reply_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            bot_logger.log_admin_action(user_id, "VIEW_SETTINGS_MENU", "Viewed system settings menu")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_system_settings_menu")

    def get_current_menu(self, context: ContextTypes.DEFAULT_TYPE) -> str:
        """Get current menu state"""
        return context.user_data.get('current_menu', 'main')

    async def handle_back_to_main(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle back to main menu"""
        try:
            await self.show_main_admin_menu(update, context)
        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_back_to_main")


# Create service instance
admin_menu_service = AdminMenuService()
