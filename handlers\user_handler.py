from telegram import Update, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>emove
from telegram.ext import ContextTypes
from logger import bot_logger, logger
from messages import UNKNOWN_COMMAND, SEND_RECEIPT_IMAGE
from error_handler import error_handler
from services.user_service import UserService
from services.purchase_service import purchase_service
from services.single_purchase_service import single_purchase_service
from services.wallet_service import wallet_service
from services.settings_service import settings_service
from handlers.start_handler import StartHandler
from utils.auth import AuthUtils
from utils.forced_join_middleware import forced_join_middleware
from utils.ban_middleware import ban_middleware

class UserHandler:
    def __init__(self):
        self.user_service = UserService()
        self.auth_utils = AuthUtils()
        logger.info("UserHandler initialized successfully")

    async def _check_access_permissions(self, update: Update, context: ContextTypes.DEFAULT_TYPE,
                                      button_key: str = None, check_sales: bool = False) -> bool:
        """
        Check if user has access to a specific function
        Returns True if access is allowed, False otherwise
        """
        try:
            user_id = update.effective_user.id
            username = update.effective_user.username or "N/A"

            # Always reload settings to get the latest state
            settings = settings_service.load_settings()
            general_settings = settings.get("general", {})
            user_buttons = settings.get("user_buttons", {})

            # Check maintenance mode first
            if general_settings.get("maintenance_mode", False):
                await update.message.reply_text(
                    "🔧 **ربات در حال تعمیر است**\n\nلطفاً منتظر باشید و بعداً دوباره دستور /start را ارسال کنید.",
                    parse_mode='Markdown',
                    reply_markup=ReplyKeyboardRemove()
                )
                bot_logger.log_user_action(user_id, username, "MAINTENANCE_ACCESS", f"User tried to access during maintenance")
                return False

            # Check if user is banned (skip for admins)
            if not await self.auth_utils.is_admin(user_id):
                if not await ban_middleware.check_user_ban_status(update, context):
                    return False

            # Check forced join channels membership (skip for admins)
            if not await self.auth_utils.is_admin(user_id):
                if not await forced_join_middleware.check_user_access(update, context):
                    bot_logger.log_user_action(user_id, username, "FORCED_JOIN_BLOCKED", f"User blocked due to missing channel memberships")
                    return False

            # Check if specific button is enabled
            if button_key and not user_buttons.get(f"{button_key}_enabled", True):
                start_handler = StartHandler()
                button_display_names = {
                    "apple_ids": "مشاهده Apple ID ها",
                    "prices": "قیمت ها",
                    "purchase_plans": "پلن‌های خرید",
                    "wallet": "کیف پول",
                    "support": "پشتیبانی",
                    "help": "راهنما"
                }
                display_name = button_display_names.get(button_key, button_key)
                await start_handler.update_user_keyboard_only(
                    update, context,
                    f"❌ **{display_name} فعلاً در دسترس نیست**\n\nکیبورد بروزرسانی شد. لطفاً بعداً امتحان کنید یا دوباره دستور /start را ارسال کنید."
                )
                return False

            # Check sales status for purchase-related functions
            if check_sales and not general_settings.get("sales_enabled", True):
                start_handler = StartHandler()
                await start_handler.update_user_keyboard_only(
                    update, context,
                    f"❌ **فروش فعلاً بسته است**\n\nکیبورد بروزرسانی شد. لطفاً بعداً امتحان کنید یا دوباره دستور /start را ارسال کنید."
                )
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking access permissions: {str(e)}")
            return False

    async def handle_user_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE, message_text: str) -> None:
        """Handle regular user messages"""
        try:
            user_id = update.effective_user.id
            username = update.effective_user.username or "N/A"

            # Load settings
            settings = settings_service.load_settings()
            general_settings = settings.get("general", {})

            # Check maintenance mode
            if general_settings.get("maintenance_mode", False):
                # In maintenance mode, remove keyboard completely
                await update.message.reply_text(
                    "🔧 **ربات در حال تعمیر است**\n\nلطفاً منتظر باشید و بعداً دوباره دستور /start را ارسال کنید.",
                    parse_mode='Markdown',
                    reply_markup=ReplyKeyboardRemove()
                )
                bot_logger.log_user_action(user_id, username, "MAINTENANCE_ACCESS", f"User tried to access during maintenance: {message_text}")
                return

            # Check if user is in deposit process
            if context.user_data.get('waiting_for_deposit_amount'):
                await wallet_service.process_deposit_amount(update, context, message_text)
                return

            elif context.user_data.get('waiting_for_receipt'):
                if update.message.photo:
                    await wallet_service.process_receipt(update, context)
                else:
                    await update.message.reply_text(SEND_RECEIPT_IMAGE)
                return

            # Check if user is in single purchase process
            purchase_step = context.user_data.get('purchase_step')
            if purchase_step == 'waiting_receipt':
                if message_text == "/cancel":
                    await single_purchase_service.cancel_purchase(update, context)
                elif update.message.photo:
                    await single_purchase_service.process_payment_receipt(update, context)
                else:
                    await update.message.reply_text("📷 لطفاً عکس فیش واریز را ارسال کنید یا برای لغو سفارش /cancel را ارسال کنید.")
                return



            # Regular menu handling with dynamic text support
            apple_ids_text = settings_service.get_button_text("apple_ids")
            prices_text = settings_service.get_button_text("prices")
            purchase_plans_text = settings_service.get_button_text("purchase_plans")
            wallet_text = settings_service.get_button_text("wallet")
            support_text = settings_service.get_button_text("support")
            help_text = settings_service.get_button_text("help")
            single_purchase_text = settings_service.get_button_text("single_purchase")

            if message_text == apple_ids_text:
                if await self._check_access_permissions(update, context, "apple_ids"):
                    await self.user_service.show_apple_ids(update, context)
            elif message_text == prices_text:
                if await self._check_access_permissions(update, context, "prices"):
                    await self.user_service.show_prices(update, context)
            elif message_text == single_purchase_text:
                if await self._check_access_permissions(update, context, check_sales=True):
                    await single_purchase_service.show_purchase_menu(update, context)
            elif message_text == purchase_plans_text:
                if await self._check_access_permissions(update, context, "purchase_plans", check_sales=True):
                    await purchase_service.show_purchase_plans(update, context)
            elif message_text == wallet_text:
                if await self._check_access_permissions(update, context, "wallet"):
                    await wallet_service.show_wallet_menu(update, context)
            elif message_text == support_text:
                if await self._check_access_permissions(update, context, "support"):
                    await self.user_service.show_support(update, context)
            elif message_text == help_text:
                if await self._check_access_permissions(update, context, "help"):
                    await self.user_service.show_help(update, context)
            else:
                await update.message.reply_text(UNKNOWN_COMMAND)

            bot_logger.log_user_action(user_id, username, "USER_MESSAGE", message_text)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_user_message")
