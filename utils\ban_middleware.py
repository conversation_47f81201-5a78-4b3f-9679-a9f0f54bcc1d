from telegram import Update, Reply<PERSON><PERSON><PERSON><PERSON>emove
from telegram.ext import ContextTypes
from logger import logger, bot_logger
from database_manager import db_manager
from utils.auth import AuthUtils


class BanMiddleware:
    def __init__(self):
        self.auth_utils = AuthUtils()

    async def check_user_ban_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """
        Check if user is banned and handle accordingly
        Returns True if user can continue, False if user is banned
        """
        try:
            user_id = update.effective_user.id
            
            # Skip ban check for admins
            if await self.auth_utils.is_admin(user_id):
                return True
            
            # Check if user is banned
            is_banned = await db_manager.is_user_banned(user_id)
            
            if is_banned:
                await self.handle_banned_user(update, context)
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking ban status for user {user_id}: {str(e)}")
            # In case of error, allow access (fail-safe)
            return True

    async def handle_banned_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle banned user interaction"""
        try:
            user_id = update.effective_user.id
            username = update.effective_user.username or "N/A"
            
            # Remove keyboard and show ban message
            ban_message = """🚫 **دسترسی مسدود شده**

متأسفانه دسترسی شما به این ربات توسط ادمین مسدود شده است.

📞 **برای رفع مسدودیت:**
لطفاً با پشتیبانی تماس بگیرید و دلیل مسدودیت را استعلام کنید.

⚠️ **توجه:**
تا زمان رفع مسدودیت، امکان استفاده از ربات وجود ندارد.

اگر فکر میکنید که دسترسی به ربات براتون فعال شده دوباره دستور /start را ارسال کنید."""

            # Send ban message and remove keyboard
            if update.message:
                await update.message.reply_text(
                    ban_message,
                    parse_mode='Markdown',
                    reply_markup=ReplyKeyboardRemove()
                )
            elif update.callback_query:
                await update.callback_query.answer(
                    "🚫 دسترسی شما مسدود شده است",
                    show_alert=True
                )
                await update.callback_query.edit_message_text(
                    ban_message,
                    parse_mode='Markdown'
                )

            # Log banned user attempt
            bot_logger.log_user_action(user_id, username, "BANNED_ACCESS_ATTEMPT", "Banned user tried to access bot")
            
        except Exception as e:
            logger.error(f"Error handling banned user {user_id}: {str(e)}")


# Create global instance
ban_middleware = BanMiddleware()
