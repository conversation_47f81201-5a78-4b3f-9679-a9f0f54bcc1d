from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

import aiosqlite

from logger import logger, bot_logger
from error_handler import error_handler
from database_manager import db_manager
from utils.auth import AuthUtils
from messages import *

class UserManagementService:
    def __init__(self):
        self.auth_utils = AuthUtils()
        logger.info("UserManagementService initialized successfully")
    
    async def show_user_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show user management main menu"""
        try:
            user_id = update.effective_user.id
            
            # Get user statistics
            async with aiosqlite.connect(db_manager.sql_db_path) as db:
                # Total users
                cursor = await db.execute("SELECT COUNT(*) FROM users")
                total_users = (await cursor.fetchone())[0]
                
                # Admin users (from .env file)
                admin_count = 1  # Only main admin for now
                
                # Recent users (last 7 days)
                cursor = await db.execute("""
                    SELECT COUNT(*) FROM users 
                    WHERE datetime(created_at) >= datetime('now', '-7 days')
                """)
                recent_users = (await cursor.fetchone())[0]
            
            message = f"""👥 **مدیریت کاربران**

📊 **آمار کاربران:**
• تعداد کل کاربران: {total_users}
• ادمین‌ها: {admin_count}
• کاربران جدید (7 روز اخیر): {recent_users}

🔧 **عملیات مدیریتی:**"""

            keyboard = [
                [InlineKeyboardButton("👤 نمایش لیست کاربران", callback_data="show_users_list")],
                [InlineKeyboardButton("📊 آمار تفصیلی کاربران", callback_data="detailed_user_stats")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            if update.callback_query:
                await update.callback_query.edit_message_text(
                    message, parse_mode='Markdown', reply_markup=reply_markup
                )
            else:
                await update.message.reply_text(
                    message, parse_mode='Markdown', reply_markup=reply_markup
                )
            
            bot_logger.log_admin_action(user_id, "VIEW_USER_MGMT", "User management accessed")
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_user_management")
    
    async def show_users_list(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show list of users with pagination"""
        try:
            page = context.user_data.get('users_page', 1)
            per_page = 10
            offset = (page - 1) * per_page
            
            async with aiosqlite.connect(db_manager.sql_db_path) as db:
                # Get total count
                cursor = await db.execute("SELECT COUNT(*) FROM users")
                total_users = (await cursor.fetchone())[0]
                
                # Get users for current page
                cursor = await db.execute("""
                    SELECT user_id, username, first_name, last_name, wallet_balance, 
                           total_purchases, created_at, last_activity
                    FROM users 
                    ORDER BY created_at DESC 
                    LIMIT ? OFFSET ?
                """, (per_page, offset))
                users = await cursor.fetchall()
            
            total_pages = (total_users + per_page - 1) // per_page
            
            message = f"👥 **لیست کاربران** (صفحه {page} از {total_pages})\n\n"
            
            for user in users:
                user_id, username, first_name, last_name, wallet_balance, total_purchases, created_at, _ = user
                
                name = f"{first_name or ''} {last_name or ''}".strip() or "نامشخص"
                username_text = f"@{username}" if username else "بدون نام کاربری"
                
                is_admin = self.auth_utils.is_admin(user_id)
                admin_badge = " 👑" if is_admin else ""
                
                message += f"👤 **{name}**{admin_badge}\n"
                message += f"🆔 ID: `{user_id}`\n"
                message += f"📱 {username_text}\n"
                message += f"💰 موجودی: {wallet_balance:,} تومان\n"
                message += f"🛒 خریدها: {total_purchases}\n"
                message += f"📅 عضویت: {created_at[:10] if created_at else 'نامشخص'}\n\n"
            
            # Pagination buttons
            keyboard = []
            nav_buttons = []
            
            if page > 1:
                nav_buttons.append(InlineKeyboardButton("⬅️ قبلی", callback_data=f"users_page_{page-1}"))
            if page < total_pages:
                nav_buttons.append(InlineKeyboardButton("➡️ بعدی", callback_data=f"users_page_{page+1}"))
            
            if nav_buttons:
                keyboard.append(nav_buttons)

            # Add back button
            keyboard.append([InlineKeyboardButton("🔙 بازگشت", callback_data="user_management")])

            reply_markup = InlineKeyboardMarkup(keyboard)
            
            # Check if this is a callback query or message
            if update.callback_query:
                await update.callback_query.edit_message_text(
                    message, parse_mode='Markdown', reply_markup=reply_markup
                )
            else:
                await update.message.reply_text(
                    message, parse_mode='Markdown', reply_markup=reply_markup
                )
            
            bot_logger.log_admin_action(
                update.effective_user.id, 
                "VIEW_USERS_LIST", 
                f"Viewed users list page {page}"
            )
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_users_list")

    async def show_detailed_user_stats(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show detailed user statistics"""
        try:
            async with aiosqlite.connect(db_manager.sql_db_path) as db:
                # Basic stats
                cursor = await db.execute("SELECT COUNT(*) FROM users")
                total_users = (await cursor.fetchone())[0]
                
                # Users with purchases
                cursor = await db.execute("SELECT COUNT(*) FROM users WHERE total_purchases > 0")
                users_with_purchases = (await cursor.fetchone())[0]
                
                # Users with wallet balance
                cursor = await db.execute("SELECT COUNT(*) FROM users WHERE wallet_balance > 0")
                users_with_balance = (await cursor.fetchone())[0]
                
                # Average wallet balance
                cursor = await db.execute("SELECT AVG(wallet_balance) FROM users WHERE wallet_balance > 0")
                avg_balance = (await cursor.fetchone())[0] or 0
                
                # Top spenders
                cursor = await db.execute("""
                    SELECT user_id, username, first_name, total_spent 
                    FROM users 
                    WHERE total_spent > 0 
                    ORDER BY total_spent DESC 
                    LIMIT 5
                """)
                top_spenders = await cursor.fetchall()
            
            message = f"""📊 **آمار تفصیلی کاربران**

👥 **آمار کلی:**
• کل کاربران: {total_users}
• کاربران با خرید: {users_with_purchases}
• کاربران با موجودی: {users_with_balance}
• میانگین موجودی: {avg_balance:,.0f} تومان

🏆 **برترین خریداران:**"""

            if top_spenders:
                for i, (user_id, username, first_name, total_spent) in enumerate(top_spenders, 1):
                    name = first_name or username or f"User {user_id}"
                    message += f"\n{i}. {name}: {total_spent:,} تومان"
            else:
                message += "\nهنوز خریدی انجام نشده است"

            keyboard = [
                [InlineKeyboardButton("🔄 بروزرسانی", callback_data="detailed_user_stats")],
                [InlineKeyboardButton("🔙 بازگشت", callback_data="user_management")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            # Check if this is a callback query or message
            if update.callback_query:
                await update.callback_query.edit_message_text(
                    message, parse_mode='Markdown', reply_markup=reply_markup
                )
            else:
                await update.message.reply_text(
                    message, parse_mode='Markdown', reply_markup=reply_markup
                )
            
            bot_logger.log_admin_action(
                update.effective_user.id, 
                "VIEW_DETAILED_USER_STATS", 
                "Detailed user stats viewed"
            )
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_detailed_user_stats")

# Global instance
user_management_service = UserManagementService()
