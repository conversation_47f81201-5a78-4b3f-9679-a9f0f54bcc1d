# Persian messages for Telegram bot with emojis

# Welcome messages
WELCOME_ADMIN = "🔥 سلام ادمین عزیز! 👋\nبه پنل مدیریت ربات فروش Apple ID خوش آمدید 🍎"

# New comprehensive welcome message for users
WELCOME_USER_NEW = """🌟 **سلام و درود!** 👋

🍎 **به ربات فروش Apple ID خوش آمدید!**
❌ نسخه بتا 0.1

📋 **درباره ربات:**
• فروش اکانت‌های Apple ID معتبر و تست شده
• دامنه‌های مختلف: Gmail, Hotmail و سایر دامنه‌ها
• پشتیبانی 24 ساعته و گارانتی کیفیت
• خرید آسان و سریع با کیف پول داخلی

👤 **اطلاعات شما:**
🆔 **کد پشتیبانی:** `{user_id}`
👤 **نام:** {full_name}
📅 **تاریخ عضویت:** {join_date}
⏰ **آخرین فعالیت:** {last_activity}

💡 **کد پشتیبانی خود را یادداشت کنید تا در صورت نیاز به پشتیبانی، آن را ارائه دهید.**

🛒 **برای شروع خرید از دکمه‌های زیر استفاده کنید** 👇"""



# Error messages
ERROR_GENERAL = """
🚨 وای نه! یک مشکل جدی برای ربات پیش اومده که سریع باید برای ادمین ارسال کنی تا مشکل برطرف بشه 😰

📝 متن زیر رو برای ادمین ربات بفرست:

```
{error_subject}
📅 تاریخ ارور: {persian_date} - {english_date}
📋 توضیحات:
{error_description}
```
"""

ERROR_BUTTON_TEXT = "📤 ارسال به ادمین"

# Database error messages
DATABASE_ERROR = "خطا در دسترسی به دیتابیس"
EXCEL_READ_ERROR = "خطا در خواندن فایل اکسل"

# User interaction messages
LOADING = "⏳ در حال بارگذاری..."
PLEASE_WAIT = "🔄 لطفاً صبر کنید..."

# Admin messages
ADMIN_PANEL = "🔧 پنل مدیریت"
ADMIN_STATS = "📊 آمار ربات"

# Button texts
BTN_BACK = "🔙 بازگشت"
BTN_MAIN_MENU = "🏠 منوی اصلی"
BTN_REFRESH = "🔄 بروزرسانی"

# Default user button texts
DEFAULT_BTN_APPLE_IDS = "📱 مشاهده Apple ID ها"
DEFAULT_BTN_PRICES = "💰 قیمت ها"
DEFAULT_BTN_PURCHASE_PLANS = "📦 پلن‌های خرید"
DEFAULT_BTN_WALLET = "💳 کیف پول"
DEFAULT_BTN_SUPPORT = "📞 پشتیبانی"
DEFAULT_BTN_HELP = "ℹ️ راهنما"
DEFAULT_BTN_SINGLE_PURCHASE = "🛒 خرید تکی Apple ID"

# Status messages
SUCCESS = "✅ موفقیت آمیز"
FAILED = "❌ ناموفق"
PROCESSING = "⚙️ در حال پردازش..."

# Apple ID related messages
APPLE_ID_LIST = "📱 لیست Apple ID های موجود"
APPLE_ID_DETAILS = "📋 جزئیات Apple ID"
NO_APPLE_IDS = "😔 در حال حاضر Apple ID موجود نیست"

# Common responses
UNKNOWN_COMMAND = "❓ دستور نامشخص! لطفاً از منوی اصلی استفاده کنید"
ACCESS_DENIED = "🚫 شما دسترسی به این بخش ندارید"

# Purchase and wallet messages
PURCHASE_PLANS = "📦 پلن‌های خرید"
WALLET_MENU = "💳 کیف پول"
DEPOSIT_SUCCESS = "✅ واریز با موفقیت انجام شد"
DEPOSIT_REJECTED = "❌ واریز رد شد"
INSUFFICIENT_BALANCE = "❌ موجودی کافی نیست"
PURCHASE_SUCCESS = "✅ خرید با موفقیت انجام شد"

# Admin panel messages
ADMIN_PLANS_MANAGEMENT = "📦 مدیریت پلن‌ها"
ADMIN_DEPOSITS_PENDING = "💰 تراکنش‌های در انتظار"
ADMIN_USERS_MANAGEMENT = "👥 مدیریت کاربران"

# Backup messages
BACKUP_SENT = "📁 فایل‌های بکاپ ارسال شد"
BACKUP_FAILED = "❌ خطا در ایجاد بکاپ"

# Purchase service messages
NO_PURCHASE_PLANS = "در حال حاضر هیچ پلن خریدی موجود نیست"
PURCHASE_PLANS_HEADER = "**پلن‌های خرید موجود:**\n\n"
WALLET_BALANCE_TEXT = "💰 موجودی کیف پول شما: {balance:,} تومان\n\n"
PLAN_NOT_AVAILABLE = "❌ این پلن موجود نیست"
INSUFFICIENT_BALANCE_DETAILED = "❌ موجودی کافی نیست. {needed_amount:,} تومان کمبود دارید"
INSUFFICIENT_APPLE_IDS = "❌ تعداد کافی Apple ID موجود نیست. فقط {available_count} عدد موجود است"
PAYMENT_PROCESSING_ERROR = "❌ خطا در پردازش پرداخت"
PURCHASE_SUCCESS_ALERT = "✅ خرید با موفقیت انجام شد!"

# Purchase completion message
PURCHASE_COMPLETION_MESSAGE = """🎉 **خرید موفقیت‌آمیز!**

📦 پلن: {plan_name}
💰 مبلغ پرداختی: {price:,} تومان
📅 تاریخ خرید: {purchase_date}{warranty_text}

📱 **Apple ID های شما:**

{apple_ids_list}

⚠️ **نکات مهم:**
• لطفاً اطلاعات را در جای امنی نگهداری کنید
• پس از خرید می‌توانید رمز عبور را تغییر دهید{warranty_note}
• برای پشتیبانی با ادمین تماس بگیرید

✅ از خرید شما متشکریم!"""

APPLE_ID_ITEM_FORMAT = """**{index}. Apple ID:**
📧 Email: `{email}`
🔐 Password: `{password}`
🌐 Domain: {domain}

"""

WARRANTY_TEXT = "\n🛡️ گارانتی: {warranty_days} روز"
WARRANTY_NOTE = "\n• در صورت مشکل تا {warranty_days} روز گارانتی دارید"

# Wallet service messages
WALLET_HEADER = "**کیف پول شما**\n\n"
CURRENT_BALANCE = "💰 موجودی فعلی: {balance:,} تومان\n\n"
RECENT_TRANSACTIONS = "📊 **آخرین تراکنش‌ها:**\n"
WALLET_DEPOSIT_HEADER = "💰 **شارژ کیف پول**\n\n"
DEPOSIT_AMOUNT_PROMPT = """لطفاً مبلغ مورد نظر را به تومان وارد کنید:

💡 **نکات:**
• حداقل مبلغ شارژ: 10,000 تومان
• حداکثر مبلغ شارژ: 10,000,000 تومان
• فقط عدد وارد کنید (بدون کاما یا نقطه)

مثال: 50000"""

INVALID_AMOUNT = "❌ لطفاً یک عدد معتبر وارد کنید"
AMOUNT_TOO_LOW = "❌ حداقل مبلغ شارژ 10,000 تومان است"
AMOUNT_TOO_HIGH = "❌ حداکثر مبلغ شارژ 10,000,000 تومان است"

DEPOSIT_CONFIRMATION = """💰 **تایید مبلغ شارژ**

مبلغ درخواستی: {amount:,} تومان

📋 **اطلاعات پرداخت:**
• شماره کارت: 1234-5678-9012-3456
• نام صاحب حساب: نام فروشنده
• بانک: ملی

پس از واریز مبلغ، لطفاً فیش واریز را ارسال کنید.

⚠️ **نکات مهم:**
• فقط تصویر فیش واریز ارسال کنید
• فیش باید واضح و خوانا باشه
• مبلغ واریزی باید دقیقاً مطابق مبلغ درخواستی باشد"""

RECEIPT_PROCESSING_ERROR = "❌ خطا در پردازش. لطفاً مجدداً تلاش کنید"
SEND_RECEIPT_IMAGE = "❌ لطفاً تصویر فیش واریز را ارسال کنید"

RECEIPT_RECEIVED = """✅ **فیش واریز دریافت شد!**

💰 مبلغ: {amount:,} تومان
🆔 شماره تراکنش: {transaction_id}

📋 فیش شما برای بررسی به ادمین ارسال شد.
پس از تایید، مبلغ به کیف پول شما اضافه خواهد شد.

⏰ زمان بررسی: حداکثر 24 ساعت"""

PAYMENT_GROUP_DEPOSIT_REQUEST = """💰 **درخواست شارژ کیف پول**

{user_info}
💵 **مبلغ:** {amount:,} تومان
🆔 **شماره تراکنش:** {transaction_id}
📅 **تاریخ:** {date}

👆 فیش واریز در بالا ارسال شده است"""

TRANSACTION_HISTORY_HEADER = "📊 **تاریخچه تراکنش‌ها**\n\n"
NO_TRANSACTIONS = "هیچ تراکنشی یافت نشد"

# Deposit approval/rejection messages
DEPOSIT_APPROVED = """✅ **واریز شما تایید شد!**

💰 مبلغ: {amount:,} تومان
🆔 شماره تراکنش: {transaction_id}

موجودی کیف پول شما به‌روزرسانی شد.
اکنون می‌توانید از پلن‌های خرید استفاده کنید."""

DEPOSIT_REJECTED = """❌ **واریز شما رد شد**

💰 مبلغ: {amount:,} تومان
🆔 شماره تراکنش: {transaction_id}

دلیل: فیش واریز نامعتبر یا ناخوانا

لطفاً فیش واضح‌تری ارسال کنید یا با پشتیبانی تماس بگیرید."""

# Admin service messages
BOT_STATS_HEADER = "📊 **آمار کامل ربات**\n\n"
APPLE_ID_STATS = """📱 **Apple ID ها:**
• تعداد کل: {total_count}
• موجود: {available_count}
• فروخته شده: {sold_count}

"""

USER_STATS = """👥 **کاربران:**
• تعداد کل کاربران: {total_users}
• کاربران فعال (30 روز اخیر): {active_users}
• کاربران جدید (7 روز اخیر): {new_users_week}
• کاربران با خرید: {users_with_purchases}
• کاربران بن شده: {banned_users_count}
• میانگین خرید هر کاربر: {avg_purchase_per_user:.1f}

"""

SALES_STATS = """💰 **فروش:**
• تعداد خریدها: {total_purchases}
• درآمد کل: {total_revenue:,} تومان

📊 **درآمد زمان‌بندی شده:**
• 1 ماه اخیر: {revenue_1_month:,} تومان ({orders_1_month} سفارش)
• 3 ماه اخیر: {revenue_3_months:,} تومان ({orders_3_months} سفارش)
• 6 ماه اخیر: {revenue_6_months:,} تومان ({orders_6_months} سفارش)
• 1 سال اخیر: {revenue_12_months:,} تومان ({orders_12_months} سفارش)

"""

WALLET_STATS = """💳 **کیف پول:**
• موجودی کل کاربران: {total_balance:,} تومان
• تراکنش‌های در انتظار: {pending_deposits}
• واریزهای تایید شده: {approved_deposits_count} ({approved_deposits_amount:,} تومان)

"""

ADMIN_STATS = """👑 **مدیریت:**
• تعداد ادمین‌ها: {admin_count}

"""

ACTIVITY_STATS = """📈 **فعالیت کاربران:**
• فعال امروز: {daily_active_users}
• فعال این هفته: {weekly_active_users}
• فعال این ماه: {active_users}

"""

DETAILED_SALES_STATS = """📊 **جزئیات فروش (30 روز اخیر):**
• سفارشات تکی: {recent_single_orders}
• سفارشات پلنی: {recent_plan_orders}

💳 **آمار کیف پول:**
• میانگین موجودی: {avg_wallet_balance:,.0f} تومان
• بیشترین موجودی: {max_wallet_balance:,} تومان
• واریزهای رد شده: {rejected_deposits}

"""

TOP_USERS_STATS = """🏆 **برترین کاربران:**
{top_users_list}

"""

DOMAIN_STATS = """🌐 **محبوب‌ترین دامنه‌ها:**
{top_domains_list}

"""

DOMAIN_STATS_HEADER = "🌐 **آمار بر اساس دامنه ایمیل:**\n"

APPLE_ID_MANAGEMENT_HEADER = "📱 **مدیریت Apple ID ها**\n\n"
NO_APPLE_IDS_IN_DB = "📱 هیچ Apple ID در دیتابیس موجود نیست"
APPLE_ID_COUNT = "📊 تعداد کل: {count}\n\n"

PENDING_DEPOSITS_HEADER = "💰 **تراکنش‌های در انتظار تایید:**\n\n"
NO_PENDING_DEPOSITS = "💰 **تراکنش‌های در انتظار**\n\nهیچ تراکنش در انتظاری وجود ندارد"

PENDING_DEPOSIT_ITEM = """👤 **کاربر:** {user_name} ({username})
🆔 **ID:** `{user_id}`
💵 **مبلغ:** {amount:,} تومان
🆔 **شماره تراکنش:** {transaction_id}
📅 **تاریخ:** {date}
──────────────────────────────

"""

MANUAL_BACKUP_START = "📁 در حال ایجاد بکاپ..."
MANUAL_BACKUP_SUCCESS = "✅ بکاپ دستی با موفقیت انجام شد"
BACKUP_SERVICE_UNAVAILABLE = "❌ سرویس بکاپ در دسترس نیست"

DATABASE_REFRESHED = "🔄 **داده ها بروزرسانی شد**\n\n✅ اتصال دیتابیس‌ها تازه‌سازی شد"

# User service messages
PRICE_LIST_HEADER = "**لیست قیمت Apple ID ها**\n\n"
PRICE_SUMMARY = "\n📊 مجموع: {total_count} Apple ID موجود"
CONTACT_SUPPORT_FOR_PURCHASE = "\n\n📞 برای خرید با پشتیبانی تماس بگیرید"

SUPPORT_INFO = """📞 **پشتیبانی و تماس**

🆔 **کد پشتیبانی شما:** `{user_id}`

برای خرید Apple ID، پشتیبانی و سوالات خود می‌توانید با ادمین ربات تماس بگیرید:

⏰ **ساعات پاسخگویی:**
• شنبه تا چهارشنبه: 9:00 - 21:00
• پنج‌شنبه: 9:00 - 13:00
• جمعه: تعطیل

💡 **مهم:** هنگام تماس با پشتیبانی، کد پشتیبانی خود را ارائه دهید.

💬 لطفاً در پیام خود کد Apple ID مورد نظر را ذکر کنید

👇 **برای تماس با پشتیبانی روی دکمه زیر کلیک کنید:**"""

HELP_INFO = """**راهنمای استفاده از ربات**

🎯 **امکانات اصلی ربات:**

• **مشاهده Apple ID ها** - نمایش Apple ID های خریداری شده
• **خرید تکی Apple ID** - خرید فوری Apple ID
• **پلن‌های خرید** - خرید بسته‌های مختلف
• **قیمت ها** - مشاهده قیمت‌ها بر اساس کشور
• **کیف پول** - شارژ و مدیریت کیف پول
• **پشتیبانی** - تماس با پشتیبانی

👇 **برای راهنمای تفصیلی هر بخش، روی دکمه مربوطه کلیک کنید:**"""

# Help section messages
HELP_APPLE_IDS = """**راهنمای مشاهده Apple ID ها**

🔍 **چگونه Apple ID های خود را ببینم؟**
• از منوی اصلی روی دکمه مشاهده Apple ID ها کلیک کنید
• تمام Apple ID های خریداری شده نمایش داده می‌شود

📋 **اطلاعات نمایش داده شده:**
• ایمیل Apple ID
• رمز عبور
• کشور Apple ID
• تاریخ خرید
• وضعیت گارانتی

� **ارسال به صورت فایل:**
• اگر بیش از 10 Apple ID داشته باشید، به صورت فایل ارسال می‌شود
• فایل شامل تمام اطلاعات کامل است
• فایل را در مکان امن ذخیره کنید

⚠️ **نکات مهم:**
• اطلاعات را با دیگران به اشتراک نگذارید
• پس از دریافت، رمز عبور را تغییر دهید
• در صورت مشکل با پشتیبانی تماس بگیرید"""

HELP_SINGLE_PURCHASE = """� **راهنمای خرید تکی Apple ID**

🚀 **مراحل خرید تکی:**

1️⃣ **انتخاب دامنه:**
• از منوی اصلی دکمه خرید تکی Apple ID را انتخاب کنید
• کشور و قیمت مورد نظر را انتخاب کنید

2️⃣ **ثبت سفارش:**
• سفارش شما با شناسه یکتا ثبت می‌شود
• اطلاعات بانکی برای پرداخت نمایش داده می‌شود

3️⃣ **پرداخت:**
• مبلغ را به حساب نمایش داده شده واریز کنید
• عکس فیش واریز را ارسال کنید

4️⃣ **تایید:**
• سفارش شما برای تایید به ادمین ارسال می‌شود
• معمولاً ظرف 24 ساعت پاسخ داده می‌شود

5️⃣ **دریافت Apple ID:**
• پس از تایید، Apple ID کامل برای شما ارسال می‌شود

⚠️ **نکات مهم:**
• فیش واریز باید واضح و خوانا باشد
• مبلغ باید دقیقاً مطابق قیمت نمایش داده شده باشد
• تا تکمیل سفارش قبلی، نمی‌توانید سفارش جدید ثبت کنید"""

HELP_PLANS = """📦 **راهنمای پلن‌های خرید**

� **انواع پلن‌ها:**
• پلن‌های مختلف با تعداد Apple ID متفاوت
• پلن‌های با گارانتی و بدون گارانتی
• قیمت‌های متنوع برای هر بودجه

🛍️ **مراحل خرید پلن:**

1️⃣ **انتخاب پلن:**
• از دکمه پلن‌های خرید پلن مناسب را انتخاب کنید
• جزئیات پلن را بررسی کنید

2️⃣ **شارژ کیف پول:**
• کیف پول خود را به اندازه قیمت پلن شارژ کنید
• از بخش کیف پول اقدام کنید

3️⃣ **خرید:**
• پس از شارژ کافی، خرید را تایید کنید
• Apple ID ها فوراً در حساب شما قرار می‌گیرند

🛡️ **گارانتی:**
• پلن‌های دارای گارانتی در صورت مشکل تعویض می‌شوند
• مدت گارانتی در توضیحات پلن مشخص است

💡 **مزایای خرید پلن:**
• قیمت بهتر نسبت به خرید تکی
• دریافت فوری Apple ID ها
• امکان خرید با گارانتی"""

HELP_PRICES = """💰 **راهنمای قیمت‌ها**

📊 **مشاهده قیمت‌ها:**
• از منوی اصلی دکمه قیمت ها را انتخاب کنید
• قیمت‌ها بر اساس کشور نمایش داده می‌شود
• تعداد موجودی هر نوع مشخص است

🌍 **کشورهای مختلف:**
• Apple ID های کشورهای مختلف با قیمت‌های متفاوت
• کیفیت تمام Apple ID ها یکسان است
• انتخاب بر اساس نیاز شما

📈 **بروزرسانی قیمت‌ها:**
• قیمت‌ها به صورت روزانه بروزرسانی می‌شوند
• در صورت تغییر قیمت، از طریق ربات اطلاع‌رسانی می‌شود

💡 **توصیه‌ها:**
• برای خرید حجیم از پلن‌ها استفاده کنید
• قیمت‌های پلن‌ها بهتر از خرید تکی است
• قبل از خرید، موجودی را بررسی کنید"""

HELP_WALLET = """💳 **راهنمای کیف پول**

💰 **شارژ کیف پول:**

1️⃣ **انتخاب مبلغ:**
• از دکمه کیف پول گزینه "شارژ کیف پول" را انتخاب کنید
• مبلغ دلخواه را وارد کنید

2️⃣ **پرداخت:**
• اطلاعات بانکی نمایش داده می‌شود
• مبلغ را واریز کرده و فیش را ارسال کنید

3️⃣ **تایید:**
• پس از بررسی، موجودی به کیف پول شما اضافه می‌شود

📊 **مشاهده تراکنش‌ها:**
• تمام تراکنش‌های شما قابل مشاهده است
• شامل شارژ‌ها و خریدهای انجام شده

💡 **نکات مهم:**
• حداقل مبلغ شارژ: 50,000 تومان
• حداکثر مبلغ شارژ: 10,000,000 تومان
• موجودی کیف پول قابل انتقال نیست
• فیش واریز باید واضح و خوانا باشد"""

HELP_SUPPORT = """📞 **راهنمای پشتیبانی**

🤝 **چگونه با پشتیبانی تماس بگیرم؟**
• از منوی اصلی دکمه پشتیبانی را انتخاب کنید
• روی دکمه "💬 ارسال پیام به پشتیبانی" کلیک کنید
• پیام خود را ارسال کنید

⏰ **ساعات کاری:**
• شنبه تا چهارشنبه: 9:00 - 18:00
• پنج‌شنبه: 9:00 - 13:00
• جمعه: تعطیل

� **نحوه ارسال پیام:**
• پیام خود را واضح و کامل بنویسید
• در صورت مشکل با Apple ID، کد آن را ذکر کنید
• عکس یا فایل مربوطه را ضمیمه کنید

🚀 **پاسخ‌دهی:**
• پیام‌های شما در اسرع وقت پاسخ داده می‌شود
• در ساعات کاری معمولاً کمتر از 2 ساعت
• خارج از ساعات کاری تا 24 ساعت

💡 **موارد قابل پیگیری:**
• مشکلات فنی Apple ID
• سوالات خرید و پرداخت
• راهنمایی استفاده از ربات
• گزارش باگ یا پیشنهادات"""

# Plan management messages
PLANS_MANAGEMENT_HEADER = "📦 **مدیریت پلن‌های خرید**\n\n"
NO_PLANS_AVAILABLE = "هیچ پلنی موجود نیست"
PLANS_COUNT = "📊 تعداد کل پلن‌ها: {count}\n\n"

ADD_NEW_PLAN_PROMPT = """➕ **افزودن پلن جدید**

لطفاً اطلاعات پلن را به ترتیب زیر ارسال کنید:

1️⃣ نام پلن
2️⃣ تعداد Apple ID
3️⃣ قیمت (به تومان)
4️⃣ گارانتی (yes/no)
5️⃣ روزهای گارانتی (اگر دارد)
6️⃣ توضیحات

مثال:
```
پلن ویژه 15 عددی
15
350000
yes
15
پلن ویژه با گارانتی 15 روزه
```

همه اطلاعات را در یک پیام ارسال کنید."""

INCOMPLETE_PLAN_DATA = "❌ اطلاعات ناکامل. لطفاً تمام 6 فیلد را وارد کنید."
INVALID_NUMBERS = "❌ تعداد و قیمت باید عدد باشند."
INVALID_WARRANTY_DAYS = "❌ روزهای گارانتی باید عدد باشد."

PLAN_CREATED_SUCCESS = """✅ **پلن جدید با موفقیت ایجاد شد!**

🆔 ID: {plan_id}
📦 نام: {name}
📊 تعداد: {quantity}
💰 قیمت: {price:,} تومان{warranty_info}
📝 توضیحات: {description}"""

PLAN_CREATION_ERROR = "❌ خطا در ایجاد پلن"

EDIT_PLAN_MENU_HEADER = "✏️ **انتخاب پلن برای ویرایش:**\n\n"
DELETE_PLAN_MENU_HEADER = """🗑️ **انتخاب پلن برای حذف:**

⚠️ **توجه:** حذف پلن غیرقابل بازگشت است!

"""

DELETE_PLAN_CONFIRMATION = """🗑️ **تایید حذف پلن**

📦 نام: {plan_name}
📊 تعداد: {quantity}
💰 قیمت: {price:,} تومان

⚠️ آیا مطمئن هستید که می‌خواهید این پلن را حذف کنید؟"""

# Button texts
BTN_WALLET = "💳 کیف پول"
BTN_DEPOSIT = "💰 شارژ کیف پول"
BTN_HISTORY = "📊 تاریخچه تراکنش‌ها"
BTN_CANCEL = "❌ لغو"
BTN_APPROVE = "✅ تایید"
BTN_REJECT = "❌ رد"
BTN_SEND_TO_ADMIN = "📤 ارسال به ادمین"
BTN_ADD_PLAN = "➕ افزودن پلن جدید"
BTN_EDIT_PLAN = "✏️ ویرایش پلن"
BTN_DELETE_PLAN = "🗑️ حذف پلن"
BTN_CONFIRM_DELETE = "✅ بله، حذف کن"
BTN_REFRESH_STATS = "🔄 بروزرسانی"
BTN_PENDING_DEPOSITS = "💰 تراکنش‌های در انتظار"
BTN_BACK = "🔙 بازگشت"

# Alert messages
PLAN_NOT_FOUND = "پلن یافت نشد"
TRANSACTION_NOT_FOUND = "❌ تراکنش یافت نشد"
DEPOSIT_APPROVED_ALERT = "✅ واریز تایید شد"
DEPOSIT_APPROVAL_ERROR = "❌ خطا در تایید واریز"
DEPOSIT_REJECTED_ALERT = "❌ واریز رد شد"
DEPOSIT_REJECTION_ERROR = "❌ خطا در رد واریز"
PLAN_DELETED_SUCCESS = "✅ پلن با موفقیت حذف شد"
PLAN_DELETION_ERROR = "❌ خطا در حذف پلن"
NO_PLANS_TO_EDIT = "هیچ پلنی برای ویرایش موجود نیست"
NO_PLANS_TO_DELETE = "هیچ پلنی برای حذف موجود نیست"

# Single purchase messages
SINGLE_PURCHASE_NO_STOCK = """❌ **موجودی ناکافی**

متاسفانه در حال حاضر Apple ID موجود نیست.

🔄 لطفاً بعداً مجدداً تلاش کنید یا با پشتیبانی تماس بگیرید."""

SINGLE_PURCHASE_MENU_HEADER = """🛒 **خرید تکی Apple ID**

📱 **Apple ID های موجود:**

"""

SINGLE_PURCHASE_MENU_FOOTER = """⚠️ **نکات مهم:**
• پس از انتخاب، باید فیش واریز را ارسال کنید
• سفارش شما برای تایید به ادمین ارسال می‌شود
• پس از تایید، Apple ID برای شما ارسال خواهد شد
• زمان تایید معمولاً کمتر از 24 ساعت است

💡 **برای شروع خرید، دامنه مورد نظر را انتخاب کنید:**"""

SINGLE_PURCHASE_PENDING_ORDER = """⚠️ **سفارش در انتظار**

شما یک سفارش در انتظار تایید دارید.

📋 **جزئیات سفارش:**
• دامنه: {country}
• قیمت: {price:,} تومان
• تاریخ سفارش: {date}
• وضعیت: در انتظار تایید

❌ نمی‌توانید سفارش جدید ثبت کنید تا سفارش قبلی تایید یا رد شود."""

SINGLE_PURCHASE_ORDER_CREATED = """✅ **سفارش ثبت شد**

📋 **جزئیات سفارش:**
• شماره سفارش: `{order_id}`
• دامنه: {country}
• قیمت: {price:,} تومان

💳 **اطلاعات پرداخت:**
{bank_info}

📤 **مرحله بعد:**
لطفاً مبلغ را واریز کرده و عکس فیش واریز را ارسال کنید.

⚠️ **نکات مهم:**
• فیش باید واضح و خوانا باشد
• مبلغ باید دقیقاً {price:,} تومان باشد
• برای لغو سفارش /cancel را ارسال کنید"""

SINGLE_PURCHASE_RECEIPT_RECEIVED = """✅ **فیش واریز دریافت شد**

📋 **سفارش شما:**
• شماره سفارش: `{order_id}`
• دامنه: {country}
• قیمت: {price:,} تومان

⏳ **وضعیت:** در انتظار تایید ادمین

📅 سفارش شما برای بررسی و تایید به ادمین ارسال شد.
معمولاً ظرف 24 ساعت پاسخ داده می‌شود.

📱 پس از تایید، Apple ID برای شما ارسال خواهد شد."""

SINGLE_PURCHASE_APPROVAL_GROUP_MESSAGE = """🛒 **سفارش جدید - خرید تکی Apple ID**

👤 **اطلاعات کاربر:**
• نام: {first_name}
• نام کاربری: @{username}
• شناسه: `{user_id}`

📋 **جزئیات سفارش:**
• شماره سفارش: `{order_id}`
• دامنه: {country}
• قیمت: {price:,} تومان
• تاریخ سفارش: {order_date}

💳 **فیش واریز:** ⬇️"""

SINGLE_PURCHASE_APPROVED = """🎉 **سفارش شما تایید شد!**

📋 **جزئیات سفارش:**
• شماره سفارش: `{order_id}`
• دامنه: {country}
• قیمت: {price:,} تومان

📱 **Apple ID شما:**
• ایمیل: `{email}`
• رمز عبور: `{password}`
• کشور: {country}`

🔐 **سوالات امنیتی:**
• سوال 1: `{q1}`
• سوال 2: `{q2}`
• سوال 3: `{q3}`

📅 **سال تولد:** {date}

⚠️ **نکات مهم:**
• اطلاعات را در جای امن نگهداری کنید
• از تغییر اطلاعات حساب خودداری کنید
• در صورت مشکل با پشتیبانی تماس بگیرید

✅ **از خرید شما متشکریم!**"""

SINGLE_PURCHASE_REJECTED = """❌ **سفارش شما رد شد**

📋 **جزئیات سفارش:**
• شماره سفارش: `{order_id}`
• دامنه: {country}
• قیمت: {price:,} تومان

💡 **دلایل احتمالی رد:**
• فیش واریز نامعتبر یا غیرواضح
• مبلغ واریزی اشتباه
• عدم موجودی Apple ID برای کشور انتخابی

🔄 **اقدامات بعدی:**
• می‌توانید مجدداً سفارش دهید
• در صورت اشتباه با پشتیبانی تماس بگیرید
• مبلغ واریزی در صورت صحت بودن بازگردانده می‌شود

📞 **پشتیبانی:** برای پیگیری با ادمین تماس بگیرید"""

SINGLE_PURCHASE_CANCELLED = """❌ **خرید لغو شد**

تمام اطلاعات پاک شد."""

SINGLE_PURCHASE_INVALID_PRICE = "❌ قیمت نامعتبر"
SINGLE_PURCHASE_NO_RECEIPT = """❌ لطفاً عکس فیش واریز را ارسال کنید.

💡 عکس باید واضح و خوانا باشد."""

SINGLE_PURCHASE_BANK_INFO = """💳 **اطلاعات پرداخت:**

🏦 **بانک:** {bank_name}
💳 **شماره کارت:** `{card_number}`
👤 **نام صاحب حساب:** {card_holder}
🔢 **شماره شبا:** `{iban}`"""

SINGLE_PURCHASE_BANK_INFO_UPDATING = """💳 **اطلاعات پرداخت:**
اطلاعات بانکی در حال بروزرسانی است.
لطفاً با پشتیبانی تماس بگیرید."""

SINGLE_PURCHASE_BANK_INFO_ERROR = "❌ خطا در دریافت اطلاعات بانکی"

# Broadcast messages
BROADCAST_START_MESSAGE = """📢 **ارسال پیام به همه کاربران**

📝 لطفاً پیام خود را ارسال کنید:

✅ **انواع پیام قابل ارسال:**
• متن ساده
• متن همراه با عکس
• متن همراه با ویدیو
• متن همراه با فایل صوتی
• متن همراه با فایل
• متن همراه با لینک
• متن همراه با کد (```کد```)

⚠️ **نکات مهم:**
• پیام باید کمتر از 4096 کاراکتر باشد
• فایل‌ها باید کمتر از 50 مگابایت باشند
• برای لغو /cancel را ارسال کنید

📤 **پیام خود را ارسال کنید:**"""

BROADCAST_UNSUPPORTED_MESSAGE = "❌ نوع پیام پشتیبانی نمی‌شود. لطفاً متن، عکس، ویدیو یا فایل ارسال کنید."

BROADCAST_BUTTONS_COUNT_MESSAGE = """🔘 **انتخاب تعداد دکمه‌های شیشه‌ای**

چند دکمه شیشه‌ای برای این پیام نیاز دارید؟

⚠️ **نکته:** حداکثر 7 دکمه می‌توانید اضافه کنید"""

BROADCAST_BUTTON_DATA_MESSAGE = """🔘 **دکمه {button_num} از {total_buttons}**

لطفاً متن و لینک دکمه را به فرمت زیر ارسال کنید:

**فرمت:**
```
متن دکمه | لینک
```

**مثال:**
```
کانال ما | https://t.me/channel
```

⚠️ **نکات:**
• از علامت | برای جدا کردن متن و لینک استفاده کنید
• لینک باید معتبر باشد
• برای لغو /cancel را ارسال کنید"""

BROADCAST_BUTTON_FORMAT_ERROR = "❌ فرمت نادرست! لطفاً از فرمت 'متن دکمه | لینک' استفاده کنید."
BROADCAST_BUTTON_EMPTY_ERROR = "❌ متن دکمه و لینک نمی‌توانند خالی باشند."
BROADCAST_BUTTON_URL_ERROR = "❌ لینک باید با http://, https:// یا t.me/ شروع شود."

BROADCAST_PREVIEW_HEADER = """📋 **پیش‌نمایش پیام**

این پیام برای همه کاربران ارسال خواهد شد:

──────────────────────────

"""

BROADCAST_SENDING_MESSAGE = """📤 **در حال ارسال پیام...**

👥 تعداد کاربران: {total_users}
⏳ لطفاً صبر کنید..."""

BROADCAST_COMPLETED_MESSAGE = """✅ **ارسال پیام تکمیل شد**

📊 **نتایج:**
• کل کاربران: {total_users}
• ارسال موفق: {success_count}
• ارسال ناموفق: {failed_count}
• درصد موفقیت: {success_rate:.1f}%

📅 تاریخ: {date}"""

BROADCAST_CANCELLED = """❌ **ارسال پیام لغو شد**

تمام اطلاعات پاک شد."""

# Excel Management messages
EXCEL_UPLOAD_INVALID_FORMAT = "❌ فقط فایل‌های .xlsx پذیرفته می‌شوند."
EXCEL_UPLOAD_NO_FILE = "❌ لطفاً فایل اکسل را ارسال کنید."

EXCEL_VALIDATION_ERROR = """❌ **خطا در ساختار فایل:**

{error}

لطفاً فایل را تصحیح کرده و مجدداً ارسال کنید."""

EXCEL_UPLOAD_SUCCESS = """✅ **فایل اکسل با موفقیت آپلود شد**

📁 **نام فایل:** {filename}
🆕 **اکانت‌های جدید:** {new_accounts} عدد
🔢 **شروع ID:** {next_id_used}
🔢 **آخرین ID:** {final_id}

📊 **آمار کلی:**
• مجموع اکانت‌ها: {total} عدد
• اکانت‌های موجود: {available} عدد
• اکانت‌های فروخته شده: {sold} عدد

⏰ **زمان آپلود:** {upload_time}

✅ **دیتابیس بروزرسانی شد و آماده استفاده است.**"""

EXCEL_UPLOAD_ERROR = "❌ خطا در پردازش فایل:\n{error}"
EXCEL_UPLOAD_PARTIAL_SUCCESS = "✅ فایل آپلود شد اما خطا در ارسال گزارش."

# Single Account Addition messages
SINGLE_ACCOUNT_START = """➕ **اضافه کردن اکانت تکی**

لطفاً اطلاعات اکانت را به ترتیب زیر ارسال کنید:

1️⃣ **ایمیل** (email)
2️⃣ **رمز عبور** (password)
3️⃣ **سوال امنیتی 1** (q1)
4️⃣ **سوال امنیتی 2** (q2)
5️⃣ **سوال امنیتی 3** (q3)
6️⃣ **تاریخ تولد** (date)

⚠️ **نکات:**
• هر مورد را در یک پیام جداگانه ارسال کنید
• برای لغو /cancel را ارسال کنید
• ID به صورت خودکار تولید می‌شود
• قیمت بر اساس دامنه ایمیل تعریف می‌شود

📤 **ابتدا ایمیل را ارسال کنید:**"""

SINGLE_ACCOUNT_STEP_COMPLETE = "✅ {step} ثبت شد.\n\n📤 **حالا {next_step} را ارسال کنید:**"

SINGLE_ACCOUNT_INVALID_PRICE = "❌ قیمت وارد شده نامعتبر است. لطفاً عدد وارد کنید."

SINGLE_ACCOUNT_SUCCESS = """✅ **اکانت با موفقیت اضافه شد**

📋 **اطلاعات اکانت:**
🆔 **ID:** {account_id}
📧 **ایمیل:** {email}
🔐 **رمز عبور:** {password}
🌐 **دامنه:** {domain}

📁 **فایل:** acctel.xlsx
⏰ **زمان:** {add_time}

💡 **نکته:** قیمت بر اساس دامنه ایمیل تعریف می‌شود

✅ **اکانت آماده استفاده است.**"""

SINGLE_ACCOUNT_CANCELLED = """❌ **اضافه کردن اکانت لغو شد**

تمام اطلاعات پاک شد."""

# Development messages
FEATURE_IN_DEVELOPMENT = "این بخش در حال توسعه است..."
ADMIN_STATS_DEV = "📊 آمار ربات در حال توسعه..."
APPLE_ID_MGMT_DEV = "📱 مدیریت Apple ID در حال توسعه..."
USER_MGMT_DEV = "👥 مدیریت کاربران در حال توسعه..."
SETTINGS_DEV = "⚙️ تنظیمات در حال توسعه..."
LOGS_DEV = "📋 لاگ ها در حال توسعه..."
PRICES_DEV = "💰 لیست قیمت ها در حال توسعه..."
SUPPORT_DEV = "📞 پشتیبانی در حال توسعه..."
HELP_DEV = "ℹ️ راهنما در حال توسعه..."
