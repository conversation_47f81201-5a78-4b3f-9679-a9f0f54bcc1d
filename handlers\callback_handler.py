from telegram import Update
from telegram.ext import ContextTypes
from logger import logger, bot_logger
from error_handler import error_handler
from utils.auth import AuthUtils
from services.purchase_service import purchase_service
from services.wallet_service import wallet_service
from services.plan_management_service import plan_management_service
from services.user_management_service import user_management_service
from services.settings_service import settings_service
from services.broadcast_service import broadcast_service
from services.single_purchase_service import single_purchase_service
from services.excel_management_service import excel_management_service
from services.admin_management_service import admin_management_service
from services.user_service import user_service
from services.domain_price_service import DomainPriceService
from services.database_management_service import database_management_service
from services.admin_service import AdminService
from services.forced_join_service import forced_join_service
from services.user_search_service import user_search_service
from services.server_stats_service import server_stats_service
from services.stats_history_service import stats_history_service
from utils.forced_join_middleware import forced_join_middleware
from utils.ban_middleware import ban_middleware
from models.wallet import wallet_model
from messages import *

class CallbackHandler:
    def __init__(self):
        self.auth_utils = AuthUtils()
        self.domain_price_service = DomainPriceService()
        self.admin_service = AdminService()
        logger.info("CallbackHandler initialized successfully")
    
    async def handle_callback_query(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle callback queries from inline keyboards"""
        try:
            query = update.callback_query
            await query.answer()
            
            user_id = update.effective_user.id
            username = update.effective_user.username or "N/A"
            callback_data = query.data
            
            logger.debug(f"Callback query from user {user_id}: {callback_data}")
            bot_logger.log_user_action(user_id, username, "CALLBACK", callback_data)

            # Check if user is banned (skip for admins)
            if not await self.auth_utils.is_admin(user_id):
                if not await ban_middleware.check_user_ban_status(update, context):
                    return

            # Purchase plan callbacks
            if callback_data.startswith("buy_plan_"):
                plan_id = int(callback_data.split("_")[2])
                await purchase_service.process_plan_purchase(update, context, plan_id)
            
            # Wallet callbacks
            elif callback_data == "wallet_menu":
                await wallet_service.show_wallet_menu(update, context)
            
            elif callback_data == "wallet_deposit":
                await wallet_service.start_deposit_process(update, context)
            
            elif callback_data == "wallet_history":
                await wallet_service.show_transaction_history(update, context)
            
            elif callback_data == "cancel_deposit":
                context.user_data.clear()
                await query.edit_message_text("❌ درخواست شارژ لغو شد")

            # Handle back to insufficient balance scenarios
            elif callback_data == "single_purchase_menu":
                from services.single_purchase_service import single_purchase_service
                await single_purchase_service.show_single_purchase_menu(update, context)

            elif callback_data == "purchase_plans":
                from services.purchase_service import purchase_service
                await purchase_service.show_purchase_plans(update, context)
            
            # Admin deposit approval callbacks
            elif callback_data.startswith("approve_deposit_"):
                if await self.auth_utils.is_admin(user_id):
                    transaction_id = int(callback_data.split("_")[2])
                    await self.approve_deposit(update, context, transaction_id)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            elif callback_data.startswith("reject_deposit_"):
                if await self.auth_utils.is_admin(user_id):
                    transaction_id = int(callback_data.split("_")[2])
                    await self.reject_deposit(update, context, transaction_id)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            # Admin plan management callbacks
            elif callback_data == "plans_management":
                if await self.auth_utils.is_admin(user_id):
                    await plan_management_service.show_plans_management(update, context)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            elif callback_data == "add_new_plan":
                if await self.auth_utils.is_admin(user_id):
                    await plan_management_service.start_add_plan_process(update, context)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            elif callback_data.startswith("select_domain_for_plan_"):
                if await self.auth_utils.is_admin(user_id):
                    domain = callback_data.replace("select_domain_for_plan_", "")
                    await plan_management_service.process_domain_selection_for_plan(update, context, domain)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            elif callback_data.startswith("select_domain_for_purchase_"):
                domain = callback_data.replace("select_domain_for_purchase_", "")
                await purchase_service.show_plans_for_domain(update, context, domain)

            elif callback_data == "back_to_domain_selection":
                await purchase_service.show_domain_selection_for_purchase(update, context)

            elif callback_data == "edit_plan_menu":
                if await self.auth_utils.is_admin(user_id):
                    await plan_management_service.show_edit_plan_menu(update, context)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            elif callback_data.startswith("edit_plan_"):
                if await self.auth_utils.is_admin(user_id):
                    plan_id = int(callback_data.split("_")[2])
                    await plan_management_service.start_edit_plan(update, context, plan_id)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            elif callback_data == "delete_plan_menu":
                if await self.auth_utils.is_admin(user_id):
                    await plan_management_service.show_delete_plan_menu(update, context)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            elif callback_data.startswith("delete_plan_"):
                if await self.auth_utils.is_admin(user_id):
                    plan_id = int(callback_data.split("_")[2])
                    await plan_management_service.confirm_delete_plan(update, context, plan_id)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            elif callback_data.startswith("confirm_delete_plan_"):
                if await self.auth_utils.is_admin(user_id):
                    plan_id = int(callback_data.split("_")[3])
                    await plan_management_service.delete_plan(update, context, plan_id)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            elif callback_data.startswith("confirm_delete_account_"):
                if await self.auth_utils.is_admin(user_id):
                    account_id = int(callback_data.split("_")[3])
                    from services.excel_management_service import excel_management_service
                    await excel_management_service.confirm_account_delete(update, context, account_id)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            elif callback_data == "cancel_add_plan":
                if await self.auth_utils.is_admin(user_id):
                    context.user_data.pop('admin_adding_plan', None)
                    await plan_management_service.show_plans_management(update, context)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            elif callback_data == "cancel_delete_account":
                if await self.auth_utils.is_admin(user_id):
                    from services.excel_management_service import excel_management_service
                    await excel_management_service.cancel_account_delete(update, context)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            elif callback_data == "cancel_single_account":
                if await self.auth_utils.is_admin(user_id):
                    from services.excel_management_service import excel_management_service
                    await excel_management_service.cancel_single_account(update, context)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            # User management callbacks
            elif callback_data in ["user_management", "show_users_list",
                                   "detailed_user_stats"] or \
                 callback_data.startswith("users_page_"):
                if await self.auth_utils.is_admin(user_id):
                    await self.handle_user_management_callbacks(update, context, callback_data)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            # Admin management callbacks
            elif callback_data in ["admin_management", "admin_management_menu", "add_new_admin", "list_all_admins",
                                   "remove_admin_menu"] or \
                 callback_data.startswith(("confirm_remove_admin_", "execute_remove_admin_")):
                if await self.auth_utils.is_admin(user_id):
                    await self.handle_admin_management_callbacks(update, context, callback_data)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            # Domain price management callbacks
            elif callback_data in ["domain_price_menu", "domain_price_edit",
                                   "domain_price_list", "domain_price_refresh"] or callback_data.startswith("edit_domain_"):
                if await self.auth_utils.is_admin(user_id):
                    await self.handle_domain_price_callbacks(update, context, callback_data)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            # Database management callbacks
            elif callback_data in ["database_management", "db_upload_excel", "db_add_single",
                                   "db_delete_single", "db_delete_multiple", "db_download_excel", "db_manual_backup", "refresh_database"]:
                if await self.auth_utils.is_admin(user_id):
                    await self.handle_database_management_callbacks(update, context, callback_data)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            # Forced join channels management callbacks
            elif callback_data in ["forced_join_management", "add_forced_channel", "list_forced_channels",
                                   "refresh_forced_channels"] or \
                 callback_data.startswith(("toggle_channel_", "delete_channel_", "confirm_delete_channel_", "force_add_channel_")):
                if await self.auth_utils.is_admin(user_id):
                    await self.handle_forced_join_callbacks(update, context, callback_data)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            # User management callbacks
            elif callback_data.startswith(("clear_purchases_", "clear_accounts_", "change_wallet_", "ban_user_", "unban_user_")):
                if self.auth_utils.is_master_admin(user_id):
                    await self.handle_user_management_callbacks(update, context, callback_data)
                else:
                    await query.answer("❌ فقط ادمین اصلی می‌تواند این عملیات را انجام دهد", show_alert=True)

            # Account deletion callbacks
            elif callback_data.startswith("confirm_delete_account_") or callback_data == "cancel_delete_account":
                if await self.auth_utils.is_admin(user_id):
                    await self.handle_account_delete_callbacks(update, context, callback_data)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            # Admin callbacks
            elif callback_data in ["refresh_bot_stats", "refresh_apple_ids", "pending_deposits", "plans_management",
                                   "manage_single_orders", "manage_plan_orders", "refresh_server_stats",
                                   "detailed_stats_history", "stats_trend_chart", "back_to_stats_history"] or \
                 callback_data.startswith(("view_single_order_", "view_plan_order_")):
                if await self.auth_utils.is_admin(user_id):
                    await self.handle_admin_callbacks(update, context, callback_data)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            # Settings callbacks
            elif callback_data in ["settings_menu", "settings_user_buttons", "settings_button_texts", "settings_bank_info",
                                   "settings_general", "edit_card_number", "edit_card_holder",
                                   "edit_bank_name", "edit_iban"] or \
                 callback_data.startswith(("toggle_button_", "toggle_sales_", "toggle_maintenance_", "edit_button_text_")):
                if await self.auth_utils.is_admin(user_id):
                    await self.handle_settings_callbacks(update, context, callback_data)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            # Broadcast callbacks
            elif callback_data in ["start_broadcast", "confirm_broadcast", "cancel_broadcast"] or \
                 callback_data.startswith("broadcast_buttons_"):
                if await self.auth_utils.is_admin(user_id):
                    await self.handle_broadcast_callbacks(update, context, callback_data)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            # Single purchase callbacks
            elif callback_data.startswith("single_purchase_") or \
                 callback_data.startswith("approve_order_") or \
                 callback_data.startswith("reject_order_") or \
                 callback_data.startswith("approve_single_order_") or \
                 callback_data.startswith("reject_single_order_") or \
                 callback_data.startswith("approve_plan_order_") or \
                 callback_data.startswith("reject_plan_order_"):
                await self.handle_single_purchase_callbacks(update, context, callback_data)

            # Help callbacks
            elif callback_data.startswith("help_") or callback_data == "help_main":
                await self.handle_help_callbacks(update, context, callback_data)

            # Forced join membership check
            elif callback_data == "check_membership":
                await forced_join_middleware.handle_membership_check(update, context)

            # Excel management callbacks
            elif callback_data == "cancel_single_account":
                if await self.auth_utils.is_admin(user_id):
                    await excel_management_service.cancel_single_account(update, context)
                else:
                    await query.answer(ACCESS_DENIED, show_alert=True)

            else:
                await query.answer(UNKNOWN_COMMAND, show_alert=True)
        
        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_callback_query")
    
    async def approve_deposit(self, update: Update, context: ContextTypes.DEFAULT_TYPE, transaction_id: int) -> None:
        """Approve a deposit request"""
        try:
            admin_id = update.effective_user.id

            # Get transaction details before approval
            import aiosqlite
            async with aiosqlite.connect(wallet_model.db_path) as db:
                cursor = await db.execute(
                    "SELECT user_id, amount FROM wallet_transactions WHERE id = ? AND status = 'pending'",
                    (transaction_id,)
                )
                transaction = await cursor.fetchone()

            if not transaction:
                await update.callback_query.answer(TRANSACTION_NOT_FOUND, show_alert=True)
                return

            user_id, amount = transaction

            # Approve the deposit
            success = await wallet_model.approve_deposit(transaction_id, admin_id)

            if success:
                # Update the message
                original_text = update.callback_query.message.text
                updated_text = original_text + "\n\n✅ **تایید شد توسط ادمین**"

                await update.callback_query.edit_message_text(
                    updated_text,
                    parse_mode='Markdown'
                )

                await update.callback_query.answer(DEPOSIT_APPROVED_ALERT, show_alert=True)

                # Send notification to user about approved deposit
                try:
                    await context.bot.send_message(
                        chat_id=user_id,
                        text=DEPOSIT_APPROVED.format(amount=amount, transaction_id=transaction_id),
                        parse_mode='Markdown'
                    )
                except Exception as e:
                    logger.error(f"Failed to send approval notification to user {user_id}: {str(e)}")

                logger.info(f"Admin {admin_id} approved deposit {transaction_id}")
                bot_logger.log_admin_action(admin_id, "APPROVE_DEPOSIT", f"Transaction {transaction_id} approved")
            else:
                await update.callback_query.answer(DEPOSIT_APPROVAL_ERROR, show_alert=True)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "approve_deposit")
    
    async def reject_deposit(self, update: Update, context: ContextTypes.DEFAULT_TYPE, transaction_id: int) -> None:
        """Reject a deposit request"""
        try:
            admin_id = update.effective_user.id

            # Get transaction details before rejection
            import aiosqlite
            async with aiosqlite.connect(wallet_model.db_path) as db:
                cursor = await db.execute(
                    "SELECT user_id, amount FROM wallet_transactions WHERE id = ? AND status = 'pending'",
                    (transaction_id,)
                )
                transaction = await cursor.fetchone()

            if not transaction:
                await update.callback_query.answer(TRANSACTION_NOT_FOUND, show_alert=True)
                return

            user_id, amount = transaction

            # Reject the deposit
            success = await wallet_model.reject_deposit(transaction_id, admin_id, "Rejected by admin")

            if success:
                # Update the message
                original_text = update.callback_query.message.text
                updated_text = original_text + "\n\n❌ **رد شد توسط ادمین**"

                await update.callback_query.edit_message_text(
                    updated_text,
                    parse_mode='Markdown'
                )

                await update.callback_query.answer(DEPOSIT_REJECTED_ALERT, show_alert=True)

                # Send notification to user about rejected deposit
                try:
                    await context.bot.send_message(
                        chat_id=user_id,
                        text=DEPOSIT_REJECTED.format(amount=amount, transaction_id=transaction_id),
                        parse_mode='Markdown'
                    )
                except Exception as e:
                    logger.error(f"Failed to send rejection notification to user {user_id}: {str(e)}")

                logger.info(f"Admin {admin_id} rejected deposit {transaction_id}")
                bot_logger.log_admin_action(admin_id, "REJECT_DEPOSIT", f"Transaction {transaction_id} rejected")
            else:
                await update.callback_query.answer(DEPOSIT_REJECTION_ERROR, show_alert=True)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "reject_deposit")

    async def handle_user_management_callbacks(self, update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
        """Handle user management related callbacks"""
        try:
            if callback_data == "user_management":
                await user_management_service.show_user_management(update, context)

            elif callback_data == "show_users_list":
                context.user_data['users_page'] = 1
                await user_management_service.show_users_list(update, context)

            elif callback_data.startswith("users_page_"):
                page = int(callback_data.split("_")[2])
                context.user_data['users_page'] = page
                await user_management_service.show_users_list(update, context)

            elif callback_data == "detailed_user_stats":
                await user_management_service.show_detailed_user_stats(update, context)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_user_management_callbacks")

    async def handle_settings_callbacks(self, update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
        """Handle settings related callbacks"""
        try:
            if callback_data == "settings_menu":
                await settings_service.show_settings_menu(update, context)

            elif callback_data == "settings_user_buttons":
                await settings_service.show_user_buttons_settings(update, context)

            elif callback_data == "settings_button_texts":
                await settings_service.show_button_texts_settings(update, context)

            elif callback_data.startswith("edit_button_text_"):
                button_key = callback_data.split("_")[3]
                await settings_service.start_button_text_edit(update, context, button_key)

            elif callback_data.startswith("toggle_button_"):
                # Parse callback_data like "toggle_button_apple_ids_disable"
                parts = callback_data.split("_")
                if len(parts) >= 4:
                    # Handle multi-word button keys like "apple_ids"
                    action = parts[-1]  # Last part is always the action (enable/disable)
                    button_key = "_".join(parts[2:-1])  # Everything between "toggle_button_" and action

                    logger.debug(f"Toggle button: key='{button_key}', action='{action}', callback='{callback_data}'")
                    await settings_service.toggle_user_button(update, context, button_key, action)
                else:
                    logger.error(f"Invalid toggle button callback: {callback_data}")
                    await update.callback_query.answer("خطا در پردازش درخواست", show_alert=True)

            elif callback_data == "settings_bank_info":
                await settings_service.show_bank_info_settings(update, context)

            elif callback_data in ["edit_card_number", "edit_card_holder", "edit_bank_name", "edit_iban"]:
                field_name = callback_data.replace("edit_", "")
                await settings_service.start_bank_info_edit(update, context, field_name)

            elif callback_data == "settings_general":
                await settings_service.show_general_settings(update, context)

            elif callback_data.startswith("toggle_sales_"):
                action = callback_data.split("_")[2]
                await settings_service.toggle_general_setting(update, context, "sales_enabled", action)

            elif callback_data.startswith("toggle_maintenance_"):
                action = callback_data.split("_")[2]
                await settings_service.toggle_general_setting(update, context, "maintenance_mode", action)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_settings_callbacks")

    async def handle_broadcast_callbacks(self, update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
        """Handle broadcast related callbacks"""
        try:
            if callback_data == "start_broadcast":
                await broadcast_service.start_broadcast(update, context)

            elif callback_data.startswith("broadcast_buttons_"):
                count = int(callback_data.split("_")[2])
                await broadcast_service.set_buttons_count(update, context, count)

            elif callback_data == "confirm_broadcast":
                await broadcast_service.confirm_and_send_broadcast(update, context)

            elif callback_data == "cancel_broadcast":
                await broadcast_service.cancel_broadcast(update, context)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_broadcast_callbacks")

    async def handle_single_purchase_callbacks(self, update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
        """Handle single purchase related callbacks"""
        try:
            user_id = update.effective_user.id

            if callback_data.startswith("single_purchase_"):
                if callback_data.startswith("single_purchase_domain_"):
                    # New domain-based callback: single_purchase_domain_{domain}_{price}
                    parts = callback_data.split("_", 3)  # ['single', 'purchase', 'domain', '{domain}_{price}']
                    if len(parts) >= 4:
                        domain_price = parts[3]
                        domain_price_parts = domain_price.rsplit("_", 1)
                        if len(domain_price_parts) == 2:
                            domain = domain_price_parts[0]
                            price = domain_price_parts[1]
                            await single_purchase_service.start_purchase_process(update, context, domain, price)
                else:
                    # Legacy country-based callback for backward compatibility
                    parts = callback_data.split("_", 2)
                    if len(parts) >= 3:
                        country_price = parts[2]
                        country_price_parts = country_price.rsplit("_", 1)
                        if len(country_price_parts) == 2:
                            country = country_price_parts[0]
                            price = country_price_parts[1]
                            await single_purchase_service.start_purchase_process(update, context, country, price)

            elif callback_data.startswith("approve_order_"):
                if await self.auth_utils.is_admin(user_id):
                    order_id = callback_data.split("_", 2)[2]
                    await single_purchase_service.approve_order(update, context, order_id)
                else:
                    await update.callback_query.answer(ACCESS_DENIED, show_alert=True)

            elif callback_data.startswith("reject_order_"):
                if await self.auth_utils.is_admin(user_id):
                    order_id = callback_data.split("_", 2)[2]
                    await single_purchase_service.reject_order(update, context, order_id)
                else:
                    await update.callback_query.answer(ACCESS_DENIED, show_alert=True)

            elif callback_data.startswith("approve_single_order_"):
                if await self.auth_utils.is_admin(user_id):
                    order_id = callback_data.split("_", 3)[3]
                    await single_purchase_service.approve_single_order(update, context, order_id)
                else:
                    await update.callback_query.answer(ACCESS_DENIED, show_alert=True)

            elif callback_data.startswith("reject_single_order_"):
                if await self.auth_utils.is_admin(user_id):
                    order_id = callback_data.split("_", 3)[3]
                    await single_purchase_service.reject_single_order(update, context, order_id)
                else:
                    await update.callback_query.answer(ACCESS_DENIED, show_alert=True)

            elif callback_data.startswith("approve_plan_order_"):
                if await self.auth_utils.is_admin(user_id):
                    order_id = callback_data.split("_", 3)[3]
                    await purchase_service.approve_plan_order(update, context, order_id)
                else:
                    await update.callback_query.answer(ACCESS_DENIED, show_alert=True)

            elif callback_data.startswith("reject_plan_order_"):
                if await self.auth_utils.is_admin(user_id):
                    order_id = callback_data.split("_", 3)[3]
                    await purchase_service.reject_plan_order(update, context, order_id)
                else:
                    await update.callback_query.answer(ACCESS_DENIED, show_alert=True)



        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_single_purchase_callbacks")

    async def handle_help_callbacks(self, update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
        """Handle help related callbacks"""
        try:
            if callback_data == "help_main":
                # Show main help menu
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup

                keyboard = [
                    [
                        InlineKeyboardButton("📱 Apple ID ها", callback_data="help_apple_ids"),
                        InlineKeyboardButton("🛒 خرید تکی", callback_data="help_single_purchase")
                    ],
                    [
                        InlineKeyboardButton("📦 پلن‌ها", callback_data="help_plans"),
                        InlineKeyboardButton("💰 قیمت‌ها", callback_data="help_prices")
                    ],
                    [
                        InlineKeyboardButton("💳 کیف پول", callback_data="help_wallet"),
                        InlineKeyboardButton("📞 پشتیبانی", callback_data="help_support")
                    ]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.callback_query.edit_message_text(
                    HELP_INFO,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

            elif callback_data.startswith("help_"):
                section = callback_data.replace("help_", "")
                await user_service.show_help_section(update, context, section)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_help_callbacks")

    async def handle_admin_management_callbacks(self, update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
        """Handle admin management related callbacks"""
        try:
            if callback_data in ["admin_management", "admin_management_menu"]:
                await admin_management_service.show_admin_management_menu(update, context)

            elif callback_data == "add_new_admin":
                await admin_management_service.start_add_admin_process(update, context)

            elif callback_data == "list_all_admins":
                await admin_management_service.show_admins_list(update, context)

            elif callback_data == "remove_admin_menu":
                await admin_management_service.show_remove_admin_menu(update, context)

            elif callback_data.startswith("confirm_remove_admin_"):
                admin_id_to_remove = int(callback_data.split("_")[3])
                await admin_management_service.confirm_remove_admin(update, context, admin_id_to_remove)

            elif callback_data.startswith("execute_remove_admin_"):
                admin_id_to_remove = int(callback_data.split("_")[3])
                await admin_management_service.execute_remove_admin(update, context, admin_id_to_remove)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_admin_management_callbacks")

    async def handle_domain_price_callbacks(self, update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
        """Handle domain price management related callbacks"""
        try:
            if callback_data == "domain_price_menu":
                await self.domain_price_service.show_domain_price_menu(update, context)

            elif callback_data == "domain_price_list":
                await self.domain_price_service.show_domain_price_list(update, context)

            elif callback_data == "domain_price_refresh":
                await self.domain_price_service.refresh_domain_price_menu(update, context)

            elif callback_data == "domain_price_edit":
                await self.domain_price_service.show_domain_edit_menu(update, context)

            elif callback_data.startswith("edit_domain_"):
                domain = callback_data.replace("edit_domain_", "")
                await self.domain_price_service.start_edit_domain_price(update, context, domain)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_domain_price_callbacks")

    async def handle_database_management_callbacks(self, update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
        """Handle database management callbacks"""
        try:
            if callback_data == "database_management":
                await database_management_service.show_database_management_menu(update, context)
            else:
                await database_management_service.handle_database_callback(update, context, callback_data)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_database_management_callbacks")

    async def handle_account_delete_callbacks(self, update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
        """Handle account deletion callbacks"""
        try:
            if callback_data.startswith("confirm_delete_account_"):
                account_id = int(callback_data.replace("confirm_delete_account_", ""))
                await excel_management_service.confirm_account_delete(update, context, account_id)
            elif callback_data == "cancel_delete_account":
                await excel_management_service.cancel_account_delete(update, context)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_account_delete_callbacks")

    async def handle_admin_callbacks(self, update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
        """Handle admin-related callbacks"""
        try:
            if callback_data == "refresh_bot_stats":
                await self.admin_service.refresh_bot_stats(update, context)
            elif callback_data == "refresh_apple_ids":
                await self.admin_service.refresh_apple_ids(update, context)
            elif callback_data == "pending_deposits":
                await self.admin_service.show_pending_deposits(update, context)
            elif callback_data == "plans_management":
                await self.admin_service.show_plans_management(update, context)
            elif callback_data == "manage_single_orders":
                await self.admin_service.manage_single_orders(update, context)
            elif callback_data == "manage_plan_orders":
                await self.admin_service.manage_plan_orders(update, context)
            elif callback_data.startswith("view_single_order_"):
                order_id = callback_data.split("_")[-1]  # Keep as string
                await self.admin_service.view_single_order_details(update, context, order_id)
            elif callback_data.startswith("view_plan_order_"):
                order_id = callback_data.split("_")[-1]  # Keep as string
                await self.admin_service.view_plan_order_details(update, context, order_id)
            elif callback_data == "refresh_server_stats":
                await server_stats_service.refresh_server_stats(update, context)
            elif callback_data == "detailed_stats_history":
                await stats_history_service.show_detailed_stats_history(update, context)
            elif callback_data == "stats_trend_chart":
                await stats_history_service.show_revenue_trends(update, context)
            elif callback_data == "back_to_stats_history":
                await stats_history_service.show_stats_history(update, context)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_admin_callbacks")

    async def handle_forced_join_callbacks(self, update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
        """Handle forced join channels related callbacks"""
        try:
            if callback_data == "forced_join_management" or callback_data == "refresh_forced_channels":
                await forced_join_service.show_forced_join_management(update, context)

            elif callback_data == "add_forced_channel":
                await forced_join_service.start_add_channel_process(update, context)

            elif callback_data == "list_forced_channels":
                await forced_join_service.show_channels_list(update, context)

            elif callback_data.startswith("toggle_channel_"):
                # Format: toggle_channel_activate_CHANNEL_ID or toggle_channel_deactivate_CHANNEL_ID
                parts = callback_data.split("_", 3)
                if len(parts) >= 4:
                    action = parts[2]  # activate or deactivate
                    channel_id = parts[3]
                    await forced_join_service.toggle_channel_status(update, context, action, channel_id)

            elif callback_data.startswith("delete_channel_"):
                # Format: delete_channel_CHANNEL_ID
                channel_id = callback_data.replace("delete_channel_", "")
                await forced_join_service.delete_channel(update, context, channel_id)

            elif callback_data.startswith("confirm_delete_channel_"):
                # Format: confirm_delete_channel_CHANNEL_ID
                channel_id = callback_data.replace("confirm_delete_channel_", "")
                await forced_join_service.confirm_delete_channel(update, context, channel_id)

            elif callback_data.startswith("force_add_channel_"):
                # Format: force_add_channel_CHANNEL_ID
                channel_id = callback_data.replace("force_add_channel_", "")
                await forced_join_service.force_add_channel(update, context, channel_id)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_forced_join_callbacks")

    async def handle_user_management_callbacks(self, update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
        """Handle user management related callbacks"""
        try:
            user_id = update.effective_user.id

            # Only master admin can perform user management operations
            if not self.auth_utils.is_master_admin(user_id):
                await update.callback_query.answer("❌ فقط ادمین اصلی می‌تواند این عملیات را انجام دهد", show_alert=True)
                return

            if callback_data.startswith("clear_purchases_"):
                target_user_id = int(callback_data.split("_")[2])
                await self.handle_clear_purchases(update, context, target_user_id)

            elif callback_data.startswith("clear_accounts_"):
                target_user_id = int(callback_data.split("_")[2])
                await self.handle_clear_accounts(update, context, target_user_id)

            elif callback_data.startswith("change_wallet_"):
                target_user_id = int(callback_data.split("_")[2])
                await self.handle_change_wallet(update, context, target_user_id)

            elif callback_data.startswith("ban_user_"):
                target_user_id = int(callback_data.split("_")[2])
                await self.handle_ban_user(update, context, target_user_id)

            elif callback_data.startswith("unban_user_"):
                target_user_id = int(callback_data.split("_")[2])
                await self.handle_unban_user(update, context, target_user_id)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_user_management_callbacks")

    async def handle_clear_purchases(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int) -> None:
        """Handle clearing user purchases"""
        try:
            from database_manager import db_manager

            success = await db_manager.clear_user_purchases(target_user_id)

            if success:
                # Show success message first
                await update.callback_query.answer("✅ تمام خریدهای کاربر پاک شد", show_alert=True)
                bot_logger.log_admin_action(update.effective_user.id, "CLEAR_USER_PURCHASES", f"Cleared purchases for user {target_user_id}")

                # Then show user info again
                from services.user_search_service import user_search_service
                await user_search_service.show_user_info(update, context, target_user_id)
            else:
                await update.callback_query.answer("❌ خطا در پاکسازی خریدهای کاربر", show_alert=True)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_clear_purchases")

    async def handle_clear_accounts(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int) -> None:
        """Handle clearing user accounts"""
        try:
            from database_manager import db_manager

            success = await db_manager.clear_user_accounts(target_user_id)

            if success:
                # Show success message first
                await update.callback_query.answer("✅ تمام اکانت‌های کاربر آزاد شد", show_alert=True)
                bot_logger.log_admin_action(update.effective_user.id, "CLEAR_USER_ACCOUNTS", f"Cleared accounts for user {target_user_id}")

                # Then show user info again
                from services.user_search_service import user_search_service
                await user_search_service.show_user_info(update, context, target_user_id)
            else:
                await update.callback_query.answer("❌ خطا در آزادسازی اکانت‌های کاربر", show_alert=True)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_clear_accounts")

    async def handle_change_wallet(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int) -> None:
        """Handle changing user wallet balance"""
        try:
            message = f"""💰 **تغییر موجودی کیف پول**

کاربر: `{target_user_id}`

لطفاً مبلغ جدید کیف پول را به تومان ارسال کنید:

💡 **نکته:** مبلغ باید عدد صحیح باشد (بدون کاما یا نقطه)

❌ برای لغو عملیات، دستور /cancel را ارسال کنید."""

            await update.callback_query.edit_message_text(message, parse_mode='Markdown')

            # Set state for waiting wallet amount
            context.user_data['waiting_wallet_change'] = target_user_id

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_change_wallet")

    async def handle_ban_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int) -> None:
        """Handle banning a user"""
        try:
            from database_manager import db_manager

            admin_id = update.effective_user.id
            success = await db_manager.ban_user(target_user_id, admin_id, "Banned by admin")

            if success:
                await update.callback_query.edit_message_text(
                    f"🚫 **کاربر بن شد**\n\nکاربر `{target_user_id}` با موفقیت بن شد.\nاز این پس دسترسی به ربات نخواهد داشت.",
                    parse_mode='Markdown'
                )
                bot_logger.log_admin_action(admin_id, "BAN_USER", f"Banned user {target_user_id}")
            else:
                await update.callback_query.edit_message_text(
                    f"❌ **خطا**\n\nخطا در بن کردن کاربر `{target_user_id}`.",
                    parse_mode='Markdown'
                )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_ban_user")

    async def handle_unban_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int) -> None:
        """Handle unbanning a user"""
        try:
            from database_manager import db_manager

            success = await db_manager.unban_user(target_user_id)

            if success:
                await update.callback_query.edit_message_text(
                    f"✅ **بن کاربر رفع شد**\n\nکاربر `{target_user_id}` می‌تواند دوباره از ربات استفاده کند.",
                    parse_mode='Markdown'
                )
                bot_logger.log_admin_action(update.effective_user.id, "UNBAN_USER", f"Unbanned user {target_user_id}")
            else:
                await update.callback_query.edit_message_text(
                    f"❌ **خطا**\n\nخطا در رفع بن کاربر `{target_user_id}`.",
                    parse_mode='Markdown'
                )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_unban_user")

# Create global instance
callback_handler = CallbackHandler()
