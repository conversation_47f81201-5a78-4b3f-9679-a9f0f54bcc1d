from telegram import Update
from telegram.ext import ContextTypes
from logger import bot_logger, logger
from messages import UNKNOWN_COMMAND
from error_handler import error_handler
from utils.auth import AuthUtils
from services.admin_service import AdminService
from services.plan_management_service import plan_management_service
from services.settings_service import settings_service
from services.broadcast_service import broadcast_service
from services.excel_management_service import excel_management_service
from services.admin_management_service import admin_management_service
from services.domain_price_service import DomainPriceService
from services.database_management_service import database_management_service
from services.user_search_service import user_search_service
from services.server_stats_service import server_stats_service
from services.forced_join_service import forced_join_service

class AdminHandler:
    def __init__(self):
        self.admin_service = AdminService()
        self.domain_price_service = DomainPriceService()
        logger.info("AdminHandler initialized successfully")
    
    async def handle_admin_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE, message_text: str) -> None:
        """Handle admin messages"""
        try:
            user_id = update.effective_user.id

            # Check if admin is adding a plan
            if context.user_data.get('admin_adding_plan'):
                await plan_management_service.process_new_plan_data(update, context, message_text)
                return

            # Check if admin is editing bank info
            if context.user_data.get('editing_bank_field'):
                if message_text == "/cancel":
                    context.user_data.pop('editing_bank_field', None)
                    await update.message.reply_text("❌ ویرایش لغو شد")
                else:
                    await settings_service.process_bank_info_edit(update, context, message_text)
                return

            # Check if admin is searching for a user
            if context.user_data.get('waiting_user_search'):
                if message_text == "/cancel":
                    context.user_data.pop('waiting_user_search', None)
                    await update.message.reply_text("❌ جست‌وجو لغو شد")
                else:
                    await user_search_service.process_user_search(update, context, message_text)
                return

            # Check if admin is changing user wallet balance
            if context.user_data.get('waiting_wallet_change'):
                if message_text == "/cancel":
                    target_user_id = context.user_data.pop('waiting_wallet_change', None)
                    await update.message.reply_text("❌ تغییر موجودی لغو شد")
                else:
                    await self.process_wallet_change(update, context, message_text)
                return

            # Check if admin is creating broadcast
            broadcast_step = context.user_data.get('broadcast_step')
            if broadcast_step == 'waiting_message':
                if message_text == "/cancel":
                    await broadcast_service.cancel_broadcast(update, context)
                else:
                    await broadcast_service.process_broadcast_message(update, context)
                return
            elif broadcast_step == 'collecting_buttons':
                if message_text == "/cancel":
                    await broadcast_service.cancel_broadcast(update, context)
                else:
                    await broadcast_service.process_button_data(update, context, message_text)
                return

            # Check if admin is adding single account
            single_account_step = context.user_data.get('single_account_step')
            if single_account_step:
                if message_text == "/cancel":
                    await excel_management_service.cancel_single_account(update, context)
                else:
                    await excel_management_service.process_single_account_data(update, context, message_text)
                return

            if message_text == "📊 آمار ربات":
                await self.admin_service.show_bot_stats(update, context)
            elif message_text == "📱 مدیریت Apple ID":
                await self.admin_service.show_apple_id_management(update, context)
            elif message_text == "🗄️ مدیریت دیتابیس":
                await database_management_service.show_database_management_menu(update, context)
            elif message_text == "📦 مدیریت پلن‌ها":
                await self.admin_service.show_plans_management(update, context)
            elif message_text == "👥 مدیریت کاربران":
                # Only master admin can access user management
                if AuthUtils().is_master_admin(user_id):
                    await self.admin_service.show_user_management(update, context)
                else:
                    await update.message.reply_text("❌ فقط ادمین اصلی می‌تواند کاربران را مدیریت کند")
            elif message_text == "👑 مدیریت ادمین‌ها":
                # Only master admin can access admin management
                if AuthUtils().is_master_admin(user_id):
                    await admin_management_service.show_admin_management_menu(update, context)
                else:
                    await update.message.reply_text("❌ فقط ادمین اصلی می‌تواند ادمین‌های دیگر را مدیریت کند")
            elif message_text == "⚙️ تنظیمات":
                await self.admin_service.show_settings(update, context)
            elif message_text == "📱 نمایش اکانت‌ها از دیتابیس":
                await self.admin_service.show_accounts_from_database(update, context)
            elif message_text == "🔄 بروزرسانی":
                await self.admin_service.refresh_data(update, context)
            elif message_text == "💰 تراکنش‌های در انتظار":
                await self.admin_service.show_pending_deposits(update, context)
            elif message_text == "💸 مدیریت قیمت دامنه‌ها":
                await self.domain_price_service.show_domain_price_menu(update, context)
            elif message_text == "📢 مدیریت کانال‌های جوین اجباری":
                await forced_join_service.show_forced_join_management(update, context)
            elif message_text == "📁 بکاپ دستی":
                await self.admin_service.manual_backup(update, context)
            elif message_text == "📢 ارسال پیام به همه":
                await self.show_broadcast_menu(update, context)
            elif message_text == "🔍 جست و جوی کاربر":
                # Only master admin can search users
                if AuthUtils().is_master_admin(user_id):
                    await user_search_service.show_user_search_prompt(update, context)
                else:
                    await update.message.reply_text("❌ فقط ادمین اصلی می‌تواند کاربران را جست‌وجو کند")
            elif message_text == "🖥️ آمار و اطلاعات سرور":
                await server_stats_service.show_server_stats(update, context)
            else:
                await update.message.reply_text(UNKNOWN_COMMAND)

            bot_logger.log_admin_action(user_id, "ADMIN_MESSAGE", message_text)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_admin_message")

    async def process_wallet_change(self, update: Update, context: ContextTypes.DEFAULT_TYPE, amount_text: str) -> None:
        """Process wallet balance change"""
        try:
            target_user_id = context.user_data.get('waiting_wallet_change')
            if not target_user_id:
                return

            # Validate amount
            try:
                new_balance = int(amount_text.strip().replace(',', ''))
                if new_balance < 0:
                    await update.message.reply_text("❌ مبلغ نمی‌تواند منفی باشد.")
                    return
            except ValueError:
                await update.message.reply_text("❌ مبلغ وارد شده نامعتبر است. لطفاً یک عدد صحیح ارسال کنید.")
                return

            # Update wallet balance
            from database_manager import db_manager
            success = await db_manager.update_user_wallet_balance(target_user_id, new_balance)

            if success:
                await update.message.reply_text(
                    f"✅ **موجودی کیف پول بروزرسانی شد**\n\n"
                    f"کاربر: `{target_user_id}`\n"
                    f"موجودی جدید: {new_balance:,} تومان",
                    parse_mode='Markdown'
                )
                bot_logger.log_admin_action(
                    update.effective_user.id,
                    "CHANGE_USER_WALLET",
                    f"Changed wallet for user {target_user_id} to {new_balance}"
                )
            else:
                await update.message.reply_text(
                    f"❌ خطا در بروزرسانی موجودی کیف پول کاربر `{target_user_id}`.",
                    parse_mode='Markdown'
                )

            # Clear state
            context.user_data.pop('waiting_wallet_change', None)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "process_wallet_change")





    async def handle_excel_upload_request(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Excel upload request"""
        try:
            message = """📤 **آپلود فایل اکسل**

لطفاً فایل اکسل (.xlsx) خود را ارسال کنید.

⚠️ **نکات مهم:**
• فایل باید فرمت .xlsx باشد
• ستون‌های مورد نیاز: id, email, pass, q1, q2, q3, date, status
• ترتیب ستون‌ها مهم است
• ID ها به صورت خودکار تولید می‌شوند
• فایل با data.xlsx ادغام می‌شود
• ایمیل‌های تکراری نادیده گرفته می‌شوند

📋 **فرآیند:**
1. فایل شما اعتبارسنجی می‌شود
2. ایمیل‌های تکراری حذف می‌شوند
3. ID های جدید تولید می‌شوند
4. فایل با data.xlsx ادغام می‌شود
5. گزارش کامل ارسال می‌شود

💡 **نکته:** قیمت‌گذاری بر اساس دامنه ایمیل انجام می‌شود

📤 **فایل اکسل خود را ارسال کنید:**"""

            context.user_data['waiting_for_excel'] = True
            await update.message.reply_text(message, parse_mode='Markdown')

            bot_logger.log_admin_action(
                update.effective_user.id,
                "EXCEL_UPLOAD_REQUEST",
                "Admin requested Excel upload"
            )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_excel_upload_request")

    async def handle_excel_file(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Excel file upload"""
        try:
            if context.user_data.get('waiting_for_excel'):
                context.user_data.pop('waiting_for_excel', None)
                await excel_management_service.handle_excel_upload(update, context)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_excel_file")

    async def show_broadcast_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show broadcast menu"""
        try:
            message = """📢 **ارسال پیام به همه کاربران**

این قابلیت به شما امکان ارسال پیام به تمام کاربران ربات را می‌دهد.

✅ **امکانات:**
• ارسال متن، عکس، ویدیو، صدا و فایل
• اضافه کردن دکمه‌های شیشه‌ای (تا 7 عدد)
• پیش‌نمایش قبل از ارسال
• گزارش نتایج ارسال

⚠️ **نکات مهم:**
• پیام برای همه کاربران ارسال می‌شود
• امکان بازگشت وجود ندارد
• لطفاً با دقت استفاده کنید

آیا می‌خواهید ادامه دهید؟"""

            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            keyboard = [
                [InlineKeyboardButton("✅ شروع ارسال پیام", callback_data="start_broadcast")],
                [InlineKeyboardButton("❌ انصراف", callback_data="cancel_broadcast")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)

            bot_logger.log_admin_action(
                update.effective_user.id,
                "VIEW_BROADCAST_MENU",
                "Broadcast menu accessed"
            )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_broadcast_menu")
