# build_config.spec
# -*- mode: python ; coding: utf-8 -*-

import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# مسیر پروژه
project_path = os.path.abspath('.')

# فایل‌ها و پوشه‌هایی که باید exclude شوند
excludes = [
    'backups',
    'config', 
    'database',
    '.env',
    '.env.example',
    'test_*.py',
    '__pycache__',
    '.git',
    '.gitignore',
    'README.md',
    '*.log',
    'botlog.txt'
]

# Hidden imports برای telegram bot
hiddenimports = [
    'telegram',
    'telegram.ext',
    'aiosqlite',
    'openpyxl',
    'asyncio',
    'json',
    'datetime',
    'logging',
    'os',
    'sys',
    'sqlite3'
]

# Data files که باید شامل شوند (بدون excluded folders)
datas = []

# Collect all Python files except excluded ones
for root, dirs, files in os.walk('.'):
    # Remove excluded directories
    dirs[:] = [d for d in dirs if d not in excludes and not d.startswith('.')]
    
    for file in files:
        if file.endswith('.py') and not any(excl in file for excl in excludes):
            rel_path = os.path.relpath(os.path.join(root, file), '.')
            if not rel_path.startswith(tuple(excludes)):
                datas.append((os.path.join(root, file), os.path.dirname(rel_path) or '.'))

block_cipher = pyi_crypto.PyiBlockCipher(key='njakshdnjkaskfkan342qd4a854354awr1e52361r55145441r2351f54we134t6wt65w43')

a = Analysis(
    ['bot.py'],  # فایل اصلی
    pathex=[project_path],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='apple_id_bot',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # فشرده‌سازی
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)