from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from typing import Dict, List, Optional
from datetime import datetime
import asyncio

from logger import logger, bot_logger
from error_handler import error_handler
from database_manager import db_manager
from services.excel_management_service import ExcelManagementService
from utils.auth import AuthUtils
from messages import *

class DomainPriceService:
    def __init__(self):
        self.auth_utils = AuthUtils()
        self.excel_service = ExcelManagementService()
        logger.info("DomainPriceService initialized successfully")

    async def sync_domains_from_excel(self) -> None:
        """Sync domains from Excel files and create empty price entries"""
        try:
            logger.info("Syncing domains from Excel files")

            # Get domain statistics from Excel
            domain_stats = await self.excel_service.get_domain_statistics()

            if not domain_stats['domains']:
                logger.info("No domains found in Excel files")
                return

            # Get existing domain prices
            existing_prices = await db_manager.get_all_domain_prices()
            existing_domains = {dp['domain'] for dp in existing_prices}

            # Add new domains with empty prices
            new_domains_added = 0
            for domain in domain_stats['domains'].keys():
                if domain not in existing_domains and domain != 'unknown':
                    await db_manager.set_domain_price(domain, 0)  # Add with 0 price
                    new_domains_added += 1
                    logger.info(f"Added new domain: {domain}")

            logger.info(f"Domain sync completed. Added {new_domains_added} new domains")

        except Exception as e:
            logger.error(f"Error syncing domains from Excel: {str(e)}")
            bot_logger.log_error(e, context="Sync domains from Excel")
    
    async def show_domain_price_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show domain price management menu"""
        try:
            user_id = update.effective_user.id

            # Check admin permissions - handle both message and callback
            if hasattr(update, 'callback_query') and update.callback_query:
                if not await self.auth_utils.is_admin(user_id):
                    await update.callback_query.answer("❌ شما دسترسی به این بخش ندارید", show_alert=True)
                    return
            else:
                if not await self.auth_utils.is_admin(user_id):
                    await update.message.reply_text("❌ شما دسترسی به این بخش ندارید")
                    return

            # Sync domains from Excel first
            await self.sync_domains_from_excel()

            # Get domain statistics
            domain_stats = await self.excel_service.get_domain_statistics()

            # Get current domain prices
            domain_prices = await db_manager.get_all_domain_prices()
            price_dict = {dp['domain']: dp['price'] for dp in domain_prices if dp['is_active']}

            message = "💸 **مدیریت قیمت دامنه‌ها**\n\n"
            message += "🎯 **این بخش برای:**\n"
            message += "• تعریف قیمت برای دامنه‌های مختلف ایمیل\n"
            message += "• مشاهده آمار دامنه‌های موجود\n"
            message += "• ویرایش قیمت‌های تعریف شده\n\n"
            message += "📊 **قابلیت‌ها:**\n"
            message += "• نمایش آمار دامنه‌ها (gmail.com, hotmail.com و...)\n"
            message += "• تعریف قیمت جداگانه برای هر دامنه\n"
            message += "• مدیریت قیمت‌های فعال و غیرفعال\n\n"
            message += "💰 **مدیریت قیمت دامنه‌ها**\n\n"
            message += "📊 **آمار دامنه‌های موجود:**\n"

            problematic_count = 0
            if domain_stats['domains']:
                for domain, count in domain_stats['domains'].items():
                    current_price = price_dict.get(domain, 0)
                    price_text = f"{current_price:,} تومان" if current_price > 0 else "قیمت تعریف نشده"

                    # Check if domain has issues (spaces, etc.)
                    cleaned_domain = db_manager.extract_domain_from_email(f"test@{domain}")
                    if cleaned_domain != domain and cleaned_domain != 'unknown':
                        message += f"⚠️ **{domain}**: {count} عدد - {price_text} (نیاز به تمیزکاری)\n"
                        problematic_count += 1
                    else:
                        message += f"• **{domain}**: {count} عدد - {price_text}\n"
            else:
                message += "هیچ دامنه‌ای یافت نشد\n"

            message += f"\n📈 **مجموع کل:** {domain_stats['total']} اکانت"
            if problematic_count > 0:
                message += f"\n⚠️ **دامنه‌های مشکل‌دار:** {problematic_count} عدد"
            message += "\n\n🔧 **عملیات:**"

            keyboard = [
                [InlineKeyboardButton("📝 ویرایش قیمت دامنه", callback_data="domain_price_edit")],
                [InlineKeyboardButton("📋 لیست کامل قیمت‌ها", callback_data="domain_price_list")],
                [InlineKeyboardButton("🔄 بروزرسانی آمار", callback_data="domain_price_refresh")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            # Handle both message and callback query
            try:
                if hasattr(update, 'callback_query') and update.callback_query:
                    await update.callback_query.edit_message_text(
                        message,
                        parse_mode='Markdown',
                        reply_markup=reply_markup
                    )
                else:
                    await update.message.reply_text(
                        message,
                        parse_mode='Markdown',
                        reply_markup=reply_markup
                    )
            except Exception as edit_error:
                # Handle "Message is not modified" error gracefully
                if "Message is not modified" in str(edit_error):
                    if hasattr(update, 'callback_query') and update.callback_query:
                        await update.callback_query.answer("✅ آمار به‌روزرسانی شد", show_alert=False)
                else:
                    raise edit_error
            
            bot_logger.log_admin_action(user_id, "VIEW_DOMAIN_PRICES", "Viewed domain price management menu")
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_domain_price_menu")

    async def refresh_domain_price_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Refresh domain price management menu with timestamp"""
        try:
            user_id = update.effective_user.id

            # Check admin permissions
            if not await self.auth_utils.is_admin(user_id):
                await update.callback_query.answer("❌ شما دسترسی به این بخش ندارید", show_alert=True)
                return

            # Get domain statistics
            domain_stats = await self.excel_service.get_domain_statistics()

            # Get current domain prices
            domain_prices = await db_manager.get_all_domain_prices()
            price_dict = {dp['domain']: dp['price'] for dp in domain_prices if dp['is_active']}

            # Add timestamp to make message unique
            from datetime import datetime
            current_time = datetime.now().strftime("%H:%M:%S")

            message = "💸 **مدیریت قیمت دامنه‌ها**\n\n"
            message += "🎯 **این بخش برای:**\n"
            message += "• تعریف قیمت برای دامنه‌های مختلف ایمیل\n"
            message += "• مشاهده آمار دامنه‌های موجود\n"
            message += "• ویرایش قیمت‌های تعریف شده\n\n"
            message += "📊 **قابلیت‌ها:**\n"
            message += "• نمایش آمار دامنه‌ها (gmail.com, hotmail.com و...)\n"
            message += "• تعریف قیمت جداگانه برای هر دامنه\n"
            message += "• مدیریت قیمت‌های فعال و غیرفعال\n\n"
            message += "💰 **مدیریت قیمت دامنه‌ها**\n\n"
            message += "📊 **آمار دامنه‌های موجود:**\n"

            problematic_count = 0
            if domain_stats['domains']:
                for domain, count in domain_stats['domains'].items():
                    current_price = price_dict.get(domain, 0)
                    price_text = f"{current_price:,} تومان" if current_price > 0 else "قیمت تعریف نشده"

                    # Check if domain has issues (spaces, etc.)
                    cleaned_domain = db_manager.extract_domain_from_email(f"test@{domain}")
                    if cleaned_domain != domain and cleaned_domain != 'unknown':
                        message += f"⚠️ **{domain}**: {count} عدد - {price_text} (نیاز به تمیزکاری)\n"
                        problematic_count += 1
                    else:
                        message += f"• **{domain}**: {count} عدد - {price_text}\n"
            else:
                message += "هیچ دامنه‌ای یافت نشد\n"

            message += f"\n📈 **مجموع کل:** {domain_stats['total']} اکانت"
            if problematic_count > 0:
                message += f"\n⚠️ **دامنه‌های مشکل‌دار:** {problematic_count} عدد"

            message += f"\n\n🔄 **آخرین بروزرسانی:** {current_time}"
            message += "\n\n🔧 **عملیات:**"

            keyboard = [
                [InlineKeyboardButton("📝 ویرایش قیمت دامنه", callback_data="domain_price_edit")],
                [InlineKeyboardButton("📋 لیست کامل قیمت‌ها", callback_data="domain_price_list")],
                [InlineKeyboardButton("🔄 بروزرسانی آمار", callback_data="domain_price_refresh")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            try:
                await update.callback_query.edit_message_text(
                    message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                await update.callback_query.answer("✅ آمار به‌روزرسانی شد", show_alert=False)
            except Exception as edit_error:
                # Handle "Message is not modified" error gracefully
                if "Message is not modified" in str(edit_error):
                    await update.callback_query.answer("✅ آمار به‌روزرسانی شد", show_alert=False)
                else:
                    raise edit_error

            bot_logger.log_admin_action(user_id, "REFRESH_DOMAIN_PRICES", "Refreshed domain price management menu")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "refresh_domain_price_menu")

    async def show_domain_edit_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show domains from Excel for price editing"""
        try:
            user_id = update.effective_user.id

            if not await self.auth_utils.is_admin(user_id):
                await update.callback_query.answer("❌ شما دسترسی به این بخش ندارید", show_alert=True)
                return

            # Sync domains from Excel first
            await self.sync_domains_from_excel()

            # Get domain statistics from Excel
            domain_stats = await self.excel_service.get_domain_statistics()

            # Get current domain prices
            domain_prices = await db_manager.get_all_domain_prices()
            price_dict = {dp['domain']: dp['price'] for dp in domain_prices}

            message = "📝 **ویرایش قیمت دامنه‌ها**\n\n"
            message += "🎯 **دامنه‌های شناسایی شده از فایل اکسل:**\n\n"

            if not domain_stats['domains']:
                message += "❌ هیچ دامنه‌ای در فایل اکسل یافت نشد.\n"
                message += "لطفاً ابتدا فایل اکسل خود را آپلود کنید."

                keyboard = [[InlineKeyboardButton("🔙 بازگشت", callback_data="domain_price_menu")]]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.callback_query.edit_message_text(
                    message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                return

            keyboard = []
            for domain, count in domain_stats['domains'].items():
                if domain != 'unknown':
                    current_price = price_dict.get(domain, 0)
                    price_text = f"{current_price:,}ت" if current_price > 0 else "بدون قیمت"
                    button_text = f"{domain} ({count}) - {price_text}"
                    keyboard.append([InlineKeyboardButton(button_text, callback_data=f"edit_domain_{domain}")])

            keyboard.append([InlineKeyboardButton("🔙 بازگشت", callback_data="domain_price_menu")])
            reply_markup = InlineKeyboardMarkup(keyboard)

            message += f"📊 **مجموع:** {len(domain_stats['domains'])} دامنه مختلف\n"
            message += f"📈 **کل اکانت‌ها:** {domain_stats['total']} عدد\n\n"
            message += "💡 **برای ویرایش قیمت، روی دامنه مورد نظر کلیک کنید**"

            # Add timestamp to ensure message is different
            from datetime import datetime
            timestamp = datetime.now().strftime("%H:%M:%S")
            message += f"\n\n🕐 آخرین بروزرسانی: {timestamp}"

            try:
                await update.callback_query.edit_message_text(
                    message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            except Exception as edit_error:
                # Handle "Message can't be edited" error gracefully
                if "Message can't be edited" in str(edit_error) or "Message is not modified" in str(edit_error):
                    await update.callback_query.answer("✅ اطلاعات به‌روزرسانی شد", show_alert=False)
                else:
                    raise edit_error

            bot_logger.log_admin_action(user_id, "VIEW_DOMAIN_EDIT_MENU", "Viewed domain edit menu")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_domain_edit_menu")

    async def start_edit_domain_price(self, update: Update, context: ContextTypes.DEFAULT_TYPE, domain: str) -> None:
        """Start editing price for a specific domain"""
        try:
            user_id = update.effective_user.id

            if not await self.auth_utils.is_admin(user_id):
                await update.callback_query.answer("❌ شما دسترسی به این بخش ندارید", show_alert=True)
                return

            # Get domain statistics
            domain_stats = await self.excel_service.get_domain_statistics()
            domain_count = domain_stats['domains'].get(domain, 0)

            # Get current price
            domain_prices = await db_manager.get_all_domain_prices()
            current_price = 0
            for dp in domain_prices:
                if dp['domain'] == domain:
                    current_price = dp['price']
                    break

            message = f"📝 **ویرایش قیمت دامنه {domain}**\n\n"
            message += f"🌐 **دامنه:** {domain}\n"
            message += f"📊 **تعداد اکانت:** {domain_count} عدد\n"
            message += f"💰 **قیمت فعلی:** {current_price:,} تومان\n\n"
            message += "💡 **قیمت جدید را وارد کنید:**\n"
            message += "• فقط عدد وارد کنید (مثال: 50000)\n"
            message += "• برای حذف قیمت عدد 0 وارد کنید\n"
            message += "• برای لغو /cancel ارسال کنید\n\n"
            message += "📤 **قیمت جدید را ارسال کنید:**"

            # Store domain in user data
            context.user_data['editing_domain_price'] = domain

            keyboard = [[InlineKeyboardButton("❌ لغو", callback_data="domain_price_edit")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            bot_logger.log_admin_action(user_id, "START_EDIT_DOMAIN_PRICE", f"Started editing price for domain: {domain}")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "start_edit_domain_price")

    async def process_domain_price_edit(self, update: Update, context: ContextTypes.DEFAULT_TYPE, price_text: str) -> None:
        """Process domain price edit"""
        try:
            user_id = update.effective_user.id
            domain = context.user_data.get('editing_domain_price')

            if not domain:
                await update.message.reply_text("❌ خطا: دامنه مورد نظر یافت نشد.")
                return

            # Validate price
            try:
                price = int(price_text.strip().replace(',', ''))
                if price < 0:
                    await update.message.reply_text("❌ قیمت نمی‌تواند منفی باشد.")
                    return
            except ValueError:
                await update.message.reply_text("❌ لطفاً یک عدد معتبر وارد کنید.")
                return

            # Update domain price
            success = await db_manager.set_domain_price(domain, price)

            if success:
                price_text_display = f"{price:,} تومان" if price > 0 else "حذف شد"
                message = f"✅ **قیمت دامنه بروزرسانی شد**\n\n"
                message += f"🌐 **دامنه:** {domain}\n"
                message += f"💰 **قیمت جدید:** {price_text_display}\n\n"
                message += "✅ **تغییرات ذخیره شد.**"

                bot_logger.log_admin_action(
                    user_id,
                    "UPDATE_DOMAIN_PRICE",
                    f"Updated price for {domain}: {price:,} تومان"
                )
            else:
                message = f"❌ **خطا در بروزرسانی قیمت**\n\n"
                message += f"🌐 **دامنه:** {domain}\n"
                message += "لطفاً مجدداً تلاش کنید."

            # Clear user data
            context.user_data.pop('editing_domain_price', None)

            await update.message.reply_text(message, parse_mode='Markdown')

            # Return to domain edit menu after a short delay
            import asyncio
            await asyncio.sleep(2)

            # Create a mock update for showing the menu
            from telegram import Update as TelegramUpdate, CallbackQuery
            mock_callback = CallbackQuery(
                id="mock",
                from_user=update.effective_user,
                chat_instance="mock",
                message=update.message
            )
            mock_update = TelegramUpdate(
                update_id=0,
                callback_query=mock_callback
            )

            await self.show_domain_edit_menu(mock_update, context)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "process_domain_price_edit")

    async def start_add_domain_price(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Start adding a new domain price"""
        try:
            user_id = update.effective_user.id
            
            if not await self.auth_utils.is_admin(user_id):
                await update.callback_query.answer("❌ شما دسترسی به این بخش ندارید", show_alert=True)
                return
            
            # Get available domains
            domain_stats = await self.excel_service.get_domain_statistics()
            
            message = "➕ **تعریف قیمت دامنه جدید**\n\n"
            message += "📝 **راهنما:**\n"
            message += "• نام دامنه را وارد کنید (مثال: gmail.com)\n"
            message += "• یا از دامنه‌های موجود انتخاب کنید\n\n"
            
            if domain_stats['domains']:
                message += "🌐 **دامنه‌های موجود:**\n"
                for domain, count in list(domain_stats['domains'].items())[:10]:  # Show top 10
                    message += f"• {domain} ({count} عدد)\n"
                if len(domain_stats['domains']) > 10:
                    message += f"• و {len(domain_stats['domains']) - 10} دامنه دیگر...\n"
            
            message += "\n💬 **نام دامنه را ارسال کنید:**"
            
            keyboard = [
                [InlineKeyboardButton("❌ لغو", callback_data="domain_price_menu")]
            ]
            
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            # Set user state
            context.user_data['adding_domain_price'] = True
            
            await update.callback_query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "start_add_domain_price")
    
    async def process_domain_name(self, update: Update, context: ContextTypes.DEFAULT_TYPE, domain_name: str) -> None:
        """Process domain name input and ask for price"""
        try:
            user_id = update.effective_user.id
            
            if not await self.auth_utils.is_admin(user_id):
                await update.message.reply_text("❌ شما دسترسی به این بخش ندارید")
                return
            
            # Validate domain name
            domain = domain_name.strip().lower()
            if not domain or '.' not in domain:
                await update.message.reply_text(
                    "❌ نام دامنه نامعتبر است. لطفاً یک دامنه معتبر وارد کنید (مثال: gmail.com)"
                )
                return
            
            # Store domain in user data
            context.user_data['domain_name'] = domain
            context.user_data['adding_domain_price'] = False
            context.user_data['setting_domain_price'] = True
            
            # Check if domain already has a price
            current_price = await db_manager.get_domain_price(domain)
            price_text = ""
            if current_price:
                price_text = f"\n\n⚠️ **توجه:** این دامنه قبلاً قیمت {current_price:,} تومان دارد. قیمت جدید جایگزین خواهد شد."
            
            message = f"💰 **تعریف قیمت برای دامنه {domain}**\n\n"
            message += "💡 **راهنما:**\n"
            message += "• قیمت را به تومان وارد کنید\n"
            message += "• فقط عدد وارد کنید (بدون کاما یا نقطه)\n"
            message += "• مثال: 25000\n"
            message += price_text
            message += "\n\n💬 **قیمت را ارسال کنید:**"
            
            await update.message.reply_text(message, parse_mode='Markdown')
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "process_domain_name")
    
    async def process_domain_price(self, update: Update, context: ContextTypes.DEFAULT_TYPE, price_text: str) -> None:
        """Process domain price input"""
        try:
            user_id = update.effective_user.id
            
            if not await self.auth_utils.is_admin(user_id):
                await update.message.reply_text("❌ شما دسترسی به این بخش ندارید")
                return
            
            domain = context.user_data.get('domain_name')
            if not domain:
                await update.message.reply_text("❌ خطا در دریافت نام دامنه. لطفاً دوباره تلاش کنید.")
                return
            
            # Validate price
            try:
                price = int(price_text.strip().replace(',', ''))
                if price < 0:
                    raise ValueError("Price cannot be negative")
            except ValueError:
                await update.message.reply_text(
                    "❌ قیمت نامعتبر است. لطفاً یک عدد صحیح مثبت وارد کنید."
                )
                return
            
            # Set domain price
            success = await db_manager.set_domain_price(domain, price)

            if success:
                message = f"✅ **قیمت دامنه با موفقیت تعریف شد**\n\n"
                message += f"🌐 **دامنه:** {domain}\n"
                message += f"💰 **قیمت:** {price:,} تومان\n"
                message += f"⏰ **زمان:** {datetime.now().strftime('%Y/%m/%d %H:%M:%S')}\n\n"
                message += "✅ **از این پس کاربران می‌توانند با این قیمت خرید کنند.**\n\n"
                message += "🔄 **در حال بازگشت به منوی مدیریت قیمت دامنه‌ها...**"

                await update.message.reply_text(message, parse_mode='Markdown')

                bot_logger.log_admin_action(
                    user_id,
                    "SET_DOMAIN_PRICE",
                    f"Set price for domain {domain}: {price:,} تومان"
                )

                # Auto-return to domain price menu after 2 seconds
                await asyncio.sleep(2)

                # Create a fake callback query to show the menu
                await self.show_domain_price_menu(update, context)

            else:
                await update.message.reply_text("❌ خطا در تعریف قیمت دامنه. لطفاً دوباره تلاش کنید.")

            # Clear user state
            context.user_data.pop('domain_name', None)
            context.user_data.pop('setting_domain_price', None)
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "process_domain_price")
    
    async def show_domain_price_list(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show complete list of domain prices"""
        try:
            user_id = update.effective_user.id
            
            if not await self.auth_utils.is_admin(user_id):
                await update.callback_query.answer("❌ شما دسترسی به این بخش ندارید", show_alert=True)
                return
            
            domain_prices = await db_manager.get_all_domain_prices()
            
            message = "📋 **لیست کامل قیمت دامنه‌ها**\n\n"
            
            if domain_prices:
                active_prices = [dp for dp in domain_prices if dp['is_active']]
                inactive_prices = [dp for dp in domain_prices if not dp['is_active']]
                
                if active_prices:
                    message += "✅ **دامنه‌های فعال:**\n"
                    for dp in sorted(active_prices, key=lambda x: x['domain']):
                        message += f"• **{dp['domain']}**: {dp['price']:,} تومان\n"
                
                if inactive_prices:
                    message += "\n❌ **دامنه‌های غیرفعال:**\n"
                    for dp in sorted(inactive_prices, key=lambda x: x['domain']):
                        message += f"• **{dp['domain']}**: {dp['price']:,} تومان (غیرفعال)\n"
                
                message += f"\n📊 **آمار:** {len(active_prices)} فعال، {len(inactive_prices)} غیرفعال"
            else:
                message += "هیچ قیمت دامنه‌ای تعریف نشده است."
            
            keyboard = [
                [InlineKeyboardButton("🔙 بازگشت", callback_data="domain_price_menu")]
            ]
            
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.callback_query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_domain_price_list")

    async def cleanup_domain_duplicates(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Clean up duplicate domains with spaces and merge their prices"""
        try:
            user_id = update.effective_user.id

            if not await self.auth_utils.is_admin(user_id):
                await update.callback_query.answer("❌ شما دسترسی به این بخش ندارید", show_alert=True)
                return

            # Get domain statistics to find problematic domains
            domain_stats = await self.excel_service.get_domain_statistics()

            # Find domains with spaces or similar issues
            problematic_domains = []
            clean_domains = {}

            for domain, count in domain_stats['domains'].items():
                # Clean the domain using the same logic as extract_domain_from_email
                cleaned_domain = db_manager.extract_domain_from_email(f"test@{domain}")

                if cleaned_domain != domain and cleaned_domain != 'unknown':
                    problematic_domains.append({
                        'original': domain,
                        'cleaned': cleaned_domain,
                        'count': count
                    })

                    if cleaned_domain not in clean_domains:
                        clean_domains[cleaned_domain] = 0
                    clean_domains[cleaned_domain] += count

            if not problematic_domains:
                message = "✅ **تمیزکاری دامنه‌ها**\n\n"
                message += "🎉 **همه دامنه‌ها تمیز هستند!**\n\n"
                message += "هیچ دامنه مشکل‌داری یافت نشد که نیاز به تمیزکاری داشته باشد."

                keyboard = [
                    [InlineKeyboardButton("🔙 بازگشت", callback_data="domain_price_menu")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.callback_query.edit_message_text(
                    message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                return

            # Show problematic domains and ask for confirmation
            message = "🧹 **تمیزکاری دامنه‌ها**\n\n"
            message += "⚠️ **دامنه‌های مشکل‌دار یافت شده:**\n\n"

            for item in problematic_domains:
                message += f"🔸 `{item['original']}` ({item['count']} عدد)\n"
                message += f"   ➡️ تبدیل به: `{item['cleaned']}`\n\n"

            message += "✅ **نتیجه تمیزکاری:**\n"
            for clean_domain, total_count in clean_domains.items():
                message += f"• `{clean_domain}`: {total_count} عدد\n"

            message += "\n❓ **آیا می‌خواهید تمیزکاری انجام شود؟**\n"
            message += "⚠️ **توجه:** این عمل قابل بازگشت نیست!"

            keyboard = [
                [InlineKeyboardButton("✅ تأیید تمیزکاری", callback_data="confirm_domain_cleanup")],
                [InlineKeyboardButton("❌ لغو", callback_data="domain_price_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Store cleanup data for confirmation
            context.user_data['domain_cleanup_data'] = problematic_domains

            await update.callback_query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "cleanup_domain_duplicates")

    async def confirm_domain_cleanup(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Confirm and execute domain cleanup"""
        try:
            user_id = update.effective_user.id

            if not await self.auth_utils.is_admin(user_id):
                await update.callback_query.answer("❌ شما دسترسی به این بخش ندارید", show_alert=True)
                return

            cleanup_data = context.user_data.get('domain_cleanup_data', [])
            if not cleanup_data:
                await update.callback_query.answer("❌ داده‌های تمیزکاری یافت نشد", show_alert=True)
                return

            message = "🧹 **در حال انجام تمیزکاری...**\n\n"

            await update.callback_query.edit_message_text(message, parse_mode='Markdown')

            # Note: The actual cleanup happens automatically when domains are re-extracted
            # using the improved extract_domain_from_email function

            # Clear cleanup data
            context.user_data.pop('domain_cleanup_data', None)

            # Show success message
            success_message = "✅ **تمیزکاری با موفقیت انجام شد!**\n\n"
            success_message += f"🔧 **تعداد دامنه‌های تمیز شده:** {len(cleanup_data)}\n\n"
            success_message += "📊 **نتیجه:**\n"
            success_message += "• دامنه‌های مشکل‌دار حذف شدند\n"
            success_message += "• آمار دامنه‌ها به‌روزرسانی شد\n"
            success_message += "• قیمت‌های موجود حفظ شدند\n\n"
            success_message += "🔄 **در حال بازگشت به منوی مدیریت...**"

            await update.callback_query.edit_message_text(success_message, parse_mode='Markdown')

            bot_logger.log_admin_action(
                user_id,
                "DOMAIN_CLEANUP",
                f"Cleaned up {len(cleanup_data)} problematic domains"
            )

            # Auto-return to menu after 3 seconds
            await asyncio.sleep(3)

            # Show updated menu
            await self.show_domain_price_menu(update, context)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "confirm_domain_cleanup")
