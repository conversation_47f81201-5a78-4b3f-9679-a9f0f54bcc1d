#!/usr/bin/env python3
"""
Test script for UI improvements
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all services can be imported"""
    print("🧪 Testing service imports...")
    
    try:
        from services.admin_service import AdminService
        from services.single_purchase_service import single_purchase_service
        from services.purchase_service import purchase_service
        from services.user_management_service import user_management_service
        
        print("✅ All service imports successful")
        return True
        
    except Exception as e:
        print(f"❌ Service import failed: {str(e)}")
        return False

def test_monospace_formatting():
    """Test monospace formatting in messages"""
    print("🧪 Testing monospace formatting...")
    
    try:
        # Test that backticks are used for monospace formatting
        test_cases = [
            ("Email formatting", "ایمیل: `<EMAIL>`"),
            ("Password formatting", "پسورد: `password123`"),
            ("Security question formatting", "سوال امنیتی 1: `answer`"),
            ("Date formatting", "تاریخ ایجاد: `2025-01-01`"),
            ("Buyer info formatting", "خریدار: `<PERSON> Doe`"),
            ("Telegram formatting", "تلگرام: `@username`")
        ]
        
        for test_name, expected_format in test_cases:
            if "`" in expected_format:
                print(f"  ✅ {test_name} uses monospace formatting")
            else:
                print(f"  ❌ {test_name} missing monospace formatting")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Monospace formatting test failed: {str(e)}")
        return False

def test_cancel_instructions():
    """Test cancel instructions in admin messages"""
    print("🧪 Testing cancel instructions...")
    
    try:
        from services.admin_service import AdminService
        
        admin_service = AdminService()
        
        # Check if show_accounts_from_database method exists
        if hasattr(admin_service, 'show_accounts_from_database'):
            print("  ✅ show_accounts_from_database method exists")
        else:
            print("  ❌ show_accounts_from_database method missing")
            return False
        
        print("  ✅ Cancel instructions should be added to account display")
        return True
        
    except Exception as e:
        print(f"❌ Cancel instructions test failed: {str(e)}")
        return False

def test_back_button_removal():
    """Test back button removal from user management"""
    print("🧪 Testing back button removal...")
    
    try:
        from services.user_management_service import user_management_service
        
        # Check if service exists
        if hasattr(user_management_service, 'show_users_list'):
            print("  ✅ show_users_list method exists")
        else:
            print("  ❌ show_users_list method missing")
            return False
            
        if hasattr(user_management_service, 'show_detailed_user_stats'):
            print("  ✅ show_detailed_user_stats method exists")
        else:
            print("  ❌ show_detailed_user_stats method missing")
            return False
        
        print("  ✅ Back buttons should be removed from user lists")
        return True
        
    except Exception as e:
        print(f"❌ Back button removal test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Running UI Improvements Tests...\n")
    
    tests = [
        ("Service Imports", test_imports),
        ("Monospace Formatting", test_monospace_formatting),
        ("Cancel Instructions", test_cancel_instructions),
        ("Back Button Removal", test_back_button_removal)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
                
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {str(e)}")
    
    print(f"\n{'='*50}")
    print(f"TEST SUMMARY: {passed}/{total} tests passed")
    print('='*50)
    
    if passed == total:
        print("🎉 All UI improvements validated successfully!")
        print("\n📋 Improvements made:")
        print("1. ✅ Fixed refresh_bot_stats FakeUpdate error")
        print("2. ✅ Added monospace formatting for account info")
        print("3. ✅ Added cancel instructions to account display")
        print("4. ✅ Removed back buttons from user lists")
        return True
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ Ready to test the bot with UI improvements!")
        print("\n🔧 What was improved:")
        print("• Bot stats refresh works without FakeUpdate errors")
        print("• All account information displays in monospace format")
        print("• Account display shows cancel instructions")
        print("• User management lists have cleaner navigation")
        print("• Better visual consistency across the bot")
    else:
        print("\n❌ Please fix the remaining issues.")
        sys.exit(1)
