from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from logger import bot_logger, logger
from database_manager import db_manager
from messages import *
from error_handler import error_handler
from utils.auth import AuthUtils
from models.user_purchases import user_purchases_model
from services.settings_service import settings_service
import io
import os
from datetime import datetime

class UserService:
    def __init__(self):
        self.auth_utils = AuthUtils()
        logger.info("UserService initialized successfully")
    
    async def show_apple_ids(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show user's purchased Apple IDs"""
        try:
            user_id = update.effective_user.id
            logger.debug(f"User {user_id} requested Apple IDs list")

            # Send loading message and store it for deletion
            loading_message = await update.message.reply_text(LOADING)

            # Get user's purchased Apple IDs
            user_apple_ids = await user_purchases_model.get_user_apple_ids(user_id)

            if not user_apple_ids:
                logger.info(f"User {user_id} has no purchased Apple IDs")
                message = "📱 **Apple ID های شما**\n\n"
                message += "❌ شما هنوز هیچ Apple ID خریداری نکرده‌اید\n\n"
                message += f"💡 برای خرید Apple ID از منوی **{settings_service.get_button_text('purchase_plans')}** استفاده کنید"

                # Delete loading message and send final message
                try:
                    await loading_message.delete()
                except Exception as e:
                    logger.warning(f"Could not delete loading message: {e}")

                await update.message.reply_text(message, parse_mode='Markdown')
                return

            # Get user purchase stats
            stats = await user_purchases_model.get_user_purchase_stats(user_id)

            if len(user_apple_ids) <= 10:
                # Show Apple IDs in message
                logger.info(f"Sending {len(user_apple_ids)} Apple IDs as message to user {user_id}")
                message = f"📱 **Apple ID های شما** ({len(user_apple_ids)} عدد)\n\n"

                for i, apple_id in enumerate(user_apple_ids, 1):
                    # Extract domain from email
                    email = apple_id.get('email', 'نامشخص')
                    domain = 'نامشخص'
                    if email and email != 'نامشخص':
                        domain = db_manager.extract_domain_from_email(str(email))

                    message += f"🔹 **Apple ID #{i}**\n"
                    message += f"📧 Email: `{apple_id.get('email', 'نامشخص')}`\n"
                    message += f"🔐 Password: `{apple_id.get('password', 'نامشخص')}`\n"
                    message += f"🌐 Domain: {domain}\n"
                    message += f"📦 پلن: {apple_id.get('plan_name', 'نامشخص')}\n"

                    purchase_date = apple_id.get('purchase_date', '')
                    if purchase_date:
                        message += f"📅 تاریخ خرید: {purchase_date[:10]}\n"

                    # Warranty section removed as requested

                    message += "─" * 30 + "\n\n"

                message += f"📊 **آمار کلی:**\n"
                message += f"• تعداد کل خریدها: {stats['total_purchases']}\n"
                message += f"• مجموع هزینه: {stats['total_spent']:,} تومان\n"
                message += f"• گارانتی‌های فعال: {stats['active_warranties']}\n"

                # Delete loading message and send final message
                try:
                    await loading_message.delete()
                except Exception as e:
                    logger.warning(f"Could not delete loading message: {e}")

                await update.message.reply_text(message, parse_mode='Markdown')

            else:
                # Send as file
                logger.info(f"Sending {len(user_apple_ids)} Apple IDs as file to user {user_id}")
                message = f"📱 **Apple ID های شما** ({len(user_apple_ids)} عدد)\n\n"
                message += f"📊 تعداد Apple ID ها بیش از 10 عدد است، لذا به صورت فایل ارسال می‌شود.\n\n"
                message += f"📊 **آمار کلی:**\n"
                message += f"• تعداد کل Apple ID ها: {len(user_apple_ids)}\n"
                message += f"• تعداد کل خریدها: {stats['total_purchases']}\n"
                message += f"• مجموع هزینه: {stats['total_spent']:,} تومان\n"
                message += f"• گارانتی‌های فعال: {stats['active_warranties']}\n"

                # Delete loading message and send summary message
                try:
                    await loading_message.delete()
                except Exception as e:
                    logger.warning(f"Could not delete loading message: {e}")

                await update.message.reply_text(message, parse_mode='Markdown')

                # Create and send file
                file_content = user_purchases_model.format_apple_ids_as_text(user_apple_ids, user_id)
                file_name = f"apple_ids_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

                file_buffer = io.BytesIO(file_content.encode('utf-8'))
                file_buffer.name = file_name

                await context.bot.send_document(
                    chat_id=user_id,
                    document=file_buffer,
                    filename=file_name,
                    caption=f"📄 فایل Apple ID های شما\n📅 تاریخ: {datetime.now().strftime('%Y/%m/%d %H:%M')}"
                )

            bot_logger.log_user_action(
                user_id,
                update.effective_user.username or "N/A",
                "VIEW_PURCHASED_APPLE_IDS",
                f"Viewed {len(user_apple_ids)} purchased Apple IDs"
            )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_apple_ids")
    
    async def show_prices(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show price list to user based on email domains"""
        try:
            user_id = update.effective_user.id
            logger.debug(f"User {user_id} requested prices list")

            available_ids = db_manager.get_available_apple_ids()

            if not available_ids:
                logger.info(f"No Apple IDs available for price display to user {user_id}")
                await update.message.reply_text(NO_APPLE_IDS)
                return

            # Group by email domain and get prices
            domain_groups = {}
            for apple_id in available_ids:
                email = apple_id.get('Email')

                if not email:
                    continue

                # Extract domain from email
                domain = db_manager.extract_domain_from_email(str(email))

                if domain not in domain_groups:
                    domain_groups[domain] = 0
                domain_groups[domain] += 1

            # Get domain prices from database
            domain_prices = await db_manager.get_all_domain_prices()
            price_dict = {dp['domain']: dp['price'] for dp in domain_prices if dp['is_active']}

            message = "💰 **لیست قیمت Apple ID ها (بر اساس دامنه ایمیل)**\n\n"

            # Only show if there are valid domain groups
            if domain_groups:
                # Sort domains by count (descending)
                sorted_domains = sorted(domain_groups.items(), key=lambda x: x[1], reverse=True)

                for domain, count in sorted_domains:
                    # Get price for this domain
                    domain_price = price_dict.get(domain, 0)

                    if domain_price > 0:
                        price_str = f"{domain_price:,} تومان"
                    else:
                        price_str = "قیمت تعریف نشده"

                    message += f"🌐 **{domain}**\n"
                    message += f"   💰 قیمت: {price_str}\n"
                    message += f"   📊 موجود: {count} عدد\n\n"
            else:
                message += "• اطلاعات قیمت در حال بروزرسانی است\n"

            message += f"📊 **مجموع:** {len(available_ids)} Apple ID موجود\n\n"
            message += "💡 **نکته:** قیمت‌ها بر اساس دامنه ایمیل تعریف شده‌اند\n"
            message += "📞 **برای خرید با پشتیبانی تماس بگیرید**"

            await update.message.reply_text(message, parse_mode='Markdown')
            bot_logger.log_user_action(update.effective_user.id, update.effective_user.username or "N/A", "VIEW_DOMAIN_PRICES", "Domain-based price list viewed")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_prices")
    
    async def show_support(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show support information with contact button"""
        try:
            user_id = update.effective_user.id
            admin_link = os.getenv('ADMIN_LINK', '@admin')

            keyboard = [
                [InlineKeyboardButton("💬 ارسال پیام به پشتیبانی", url=f"https://t.me/{admin_link.replace('@', '')}")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Format support message with user ID
            support_message = SUPPORT_INFO.format(user_id=user_id)
            await update.message.reply_text(support_message, parse_mode='Markdown', reply_markup=reply_markup)

            bot_logger.log_user_action(
                update.effective_user.id,
                update.effective_user.username or "N/A",
                "VIEW_SUPPORT",
                "Support info viewed"
            )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_support")
    
    async def show_help(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show help information with section buttons"""
        try:
            # Get dynamic button texts
            help_icon = settings_service.get_button_text("help").split()[0] if settings_service.get_button_text("help") else "ℹ️"

            keyboard = [
                [
                    InlineKeyboardButton(settings_service.get_button_text("apple_ids"), callback_data="help_apple_ids"),
                    InlineKeyboardButton(settings_service.get_button_text("single_purchase"), callback_data="help_single_purchase")
                ],
                [
                    InlineKeyboardButton(settings_service.get_button_text("purchase_plans"), callback_data="help_plans"),
                    InlineKeyboardButton(settings_service.get_button_text("prices"), callback_data="help_prices")
                ],
                [
                    InlineKeyboardButton(settings_service.get_button_text("wallet"), callback_data="help_wallet"),
                    InlineKeyboardButton(settings_service.get_button_text("support"), callback_data="help_support")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(f"{help_icon} {HELP_INFO}", parse_mode='Markdown', reply_markup=reply_markup)

            bot_logger.log_user_action(
                update.effective_user.id,
                update.effective_user.username or "N/A",
                "VIEW_HELP",
                "Help menu viewed"
            )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_help")

    async def show_help_section(self, update: Update, context: ContextTypes.DEFAULT_TYPE, section: str) -> None:
        """Show specific help section"""
        try:
            help_messages = {
                'apple_ids': HELP_APPLE_IDS,
                'single_purchase': HELP_SINGLE_PURCHASE,
                'plans': HELP_PLANS,
                'prices': HELP_PRICES,
                'wallet': HELP_WALLET,
                'support': HELP_SUPPORT
            }

            message = help_messages.get(section, "❌ بخش مورد نظر یافت نشد")

            keyboard = [[InlineKeyboardButton("🔙 بازگشت به راهنما", callback_data="help_main")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            bot_logger.log_user_action(
                update.effective_user.id,
                update.effective_user.username or "N/A",
                "VIEW_HELP_SECTION",
                f"Help section viewed: {section}"
            )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_help_section")

# Global instance
user_service = UserService()
