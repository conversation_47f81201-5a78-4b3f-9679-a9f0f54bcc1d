from telegram import Update
from telegram.ext import ContextTypes
from logger import bot_logger, logger
from error_handler import error_handler
from utils.auth import AuthUtils
from utils.ban_middleware import ban_middleware
from utils.forced_join_middleware import forced_join_middleware
from handlers.admin_handler import Admin<PERSON><PERSON>ler
from handlers.user_handler import User<PERSON><PERSON>ler
from services.admin_management_service import admin_management_service
from services.domain_price_service import DomainPriceService
from services.forced_join_service import forced_join_service

class MessageHandler:
    def __init__(self):
        self.auth_utils = AuthUtils()
        self.admin_handler = AdminHandler()
        self.user_handler = UserHandler()
        self.domain_price_service = DomainPriceService()
        logger.info("MessageHandler initialized successfully")
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle text messages"""
        try:
            user = update.effective_user
            user_id = user.id
            username = user.username or "N/A"
            message_text = update.message.text
            
            logger.debug(f"Message received from user {user_id}: {message_text}")
            bot_logger.log_user_action(user_id, username, "MESSAGE", message_text)

            # Check if user is banned (skip for admins)
            if not await self.auth_utils.is_admin(user_id):
                if not await ban_middleware.check_user_ban_status(update, context):
                    return

            # Check forced join channels membership (skip for admins)
            if not await self.auth_utils.is_admin(user_id):
                if not await forced_join_middleware.check_user_access(update, context):
                    return

            # Check if admin is adding a new admin
            if context.user_data.get('adding_admin') and await self.auth_utils.is_admin(user_id):
                await admin_management_service.process_add_admin(update, context, message_text)
                return

            # Check if admin is adding domain price
            if context.user_data.get('adding_domain_price') and await self.auth_utils.is_admin(user_id):
                await self.domain_price_service.process_domain_name(update, context, message_text)
                return

            # Check if admin is setting domain price
            if context.user_data.get('setting_domain_price') and await self.auth_utils.is_admin(user_id):
                await self.domain_price_service.process_domain_price(update, context, message_text)
                return

            # Check if admin is editing domain price
            if context.user_data.get('editing_domain_price') and await self.auth_utils.is_admin(user_id):
                if message_text == "/cancel":
                    context.user_data.pop('editing_domain_price', None)
                    await update.message.reply_text("❌ **ویرایش قیمت دامنه لغو شد**")
                else:
                    await self.domain_price_service.process_domain_price_edit(update, context, message_text)
                return

            # Check if admin is adding forced join channel
            if context.user_data.get('adding_forced_channel') and await self.auth_utils.is_admin(user_id):
                await forced_join_service.process_add_channel(update, context, message_text)
                return

            # Check if admin is editing button text
            if context.user_data.get('editing_button_text') and await self.auth_utils.is_admin(user_id):
                from services.settings_service import settings_service
                await settings_service.process_button_text_edit(update, context, message_text)
                return

            # Check if admin is deleting account
            if context.user_data.get('deleting_account') and await self.auth_utils.is_admin(user_id):
                if message_text == "/cancel":
                    context.user_data.pop('deleting_account', None)
                    await update.message.reply_text("❌ **حذف اکانت لغو شد**\n\nهیچ تغییری اعمال نشد.")
                else:
                    from services.excel_management_service import excel_management_service
                    await excel_management_service.process_account_delete(update, context, message_text)
                return

            # Check if admin is deleting multiple accounts
            if context.user_data.get('admin_deleting_multiple') and await self.auth_utils.is_admin(user_id):
                if message_text == "/cancel":
                    context.user_data.pop('admin_deleting_multiple', None)
                    await update.message.reply_text("❌ **حذف چندتایی لغو شد**\n\nهیچ تغییری اعمال نشد.")
                else:
                    from services.database_management_service import database_management_service
                    await database_management_service.process_multiple_delete(update, context, message_text)
                return

            # Check if admin is adding plan
            if context.user_data.get('admin_adding_plan') and await self.auth_utils.is_admin(user_id):
                if message_text == "/cancel":
                    context.user_data.pop('admin_adding_plan', None)
                    context.user_data.pop('selected_domain_for_plan', None)
                    await update.message.reply_text("❌ **افزودن پلن لغو شد**\n\nهیچ تغییری اعمال نشد.")
                else:
                    from services.plan_management_service import plan_management_service
                    await plan_management_service.process_new_plan_data(update, context, message_text)
                return

            # Check if admin is specifying account range
            if context.user_data.get('waiting_for_account_range') and await self.auth_utils.is_admin(user_id):
                if message_text == "/cancel":
                    context.user_data.pop('waiting_for_account_range', None)
                    await update.message.reply_text("❌ **نمایش اکانت‌ها لغو شد**\n\nهیچ تغییری اعمال نشد.")
                else:
                    from services.admin_service import AdminService
                    admin_service = AdminService()
                    await admin_service.process_account_range(update, context, message_text)
                return

            if await self.auth_utils.is_admin(user_id):
                await self.admin_handler.handle_admin_message(update, context, message_text)
            else:
                await self.user_handler.handle_user_message(update, context, message_text)
        
        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_message")

    async def handle_document(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle document uploads"""
        try:
            user = update.effective_user
            user_id = user.id
            username = user.username or "N/A"

            logger.debug(f"Document received from user {user_id}")
            bot_logger.log_user_action(user_id, username, "DOCUMENT_UPLOAD", f"File: {update.message.document.file_name}")

            if await self.auth_utils.is_admin(user_id):
                # Check if admin is uploading Excel file
                if (context.user_data.get('waiting_for_excel') and
                    update.message.document.file_name.endswith('.xlsx')):
                    await self.admin_handler.handle_excel_file(update, context)
                # Check if admin is creating broadcast
                elif context.user_data.get('broadcast_step') == 'waiting_message':
                    from services.broadcast_service import broadcast_service
                    await broadcast_service.process_broadcast_message(update, context)
                else:
                    await update.message.reply_text("❌ فایل غیرمجاز یا درخواست نامعتبر.")
            else:
                await update.message.reply_text("❌ شما مجاز به آپلود فایل نیستید.")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_document")

    async def handle_photo(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle photo messages"""
        try:
            user = update.effective_user
            user_id = user.id
            username = user.username or "N/A"

            logger.debug(f"Photo received from user {user_id}")
            bot_logger.log_user_action(user_id, username, "PHOTO", "Photo received")

            # Check if user is in wallet deposit process
            if context.user_data.get('waiting_for_receipt'):
                await self.user_handler.handle_user_message(update, context, "")
                return

            # Check if user is in single purchase process
            purchase_step = context.user_data.get('purchase_step')
            if purchase_step == 'waiting_receipt':
                await self.user_handler.handle_user_message(update, context, "")
                return

            # Check if admin is creating broadcast
            if await self.auth_utils.is_admin(user_id) and context.user_data.get('broadcast_step') == 'waiting_message':
                from services.broadcast_service import broadcast_service
                await broadcast_service.process_broadcast_message(update, context)
                return

            # If no specific process is waiting for photo
            await update.message.reply_text("❌ در حال حاضر انتظار دریافت عکس نیست.")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_photo")

    async def handle_video(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle video messages"""
        try:
            user = update.effective_user
            user_id = user.id
            username = user.username or "N/A"

            logger.debug(f"Video received from user {user_id}")
            bot_logger.log_user_action(user_id, username, "VIDEO", "Video received")

            # Check if admin is creating broadcast
            if await self.auth_utils.is_admin(user_id) and context.user_data.get('broadcast_step') == 'waiting_message':
                from services.broadcast_service import broadcast_service
                await broadcast_service.process_broadcast_message(update, context)
                return

            # If no specific process is waiting for video
            await update.message.reply_text("❌ در حال حاضر انتظار دریافت ویدیو نیست.")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_video")

    async def handle_audio(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle audio messages"""
        try:
            user = update.effective_user
            user_id = user.id
            username = user.username or "N/A"

            logger.debug(f"Audio received from user {user_id}")
            bot_logger.log_user_action(user_id, username, "AUDIO", "Audio received")

            # Check if admin is creating broadcast
            if await self.auth_utils.is_admin(user_id) and context.user_data.get('broadcast_step') == 'waiting_message':
                from services.broadcast_service import broadcast_service
                await broadcast_service.process_broadcast_message(update, context)
                return

            # If no specific process is waiting for audio
            await update.message.reply_text("❌ در حال حاضر انتظار دریافت صدا نیست.")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_audio")
