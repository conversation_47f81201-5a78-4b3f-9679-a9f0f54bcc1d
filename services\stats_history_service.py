from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from datetime import datetime, timedelta
from logger import bot_logger, logger
from error_handler import error_handler
from utils.auth import AuthUtils
from database_manager import db_manager


class StatsHistoryService:
    def __init__(self):
        self.auth_utils = AuthUtils()

    async def show_stats_history(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show bot statistics history"""
        try:
            user_id = update.effective_user.id
            
            # Only admins can view stats history
            if not await self.auth_utils.is_admin(user_id):
                await update.message.reply_text("❌ فقط ادمین‌ها می‌توانند تاریخچه آمار را مشاهده کنند")
                return

            # Get statistics history for last 30 days
            stats_history = await db_manager.get_bot_stats_history(30)
            
            if not stats_history:
                await update.message.reply_text("📊 **تاریخچه آمار**\n\nهنوز آماری ثبت نشده است.")
                return

            message = "📊 **تاریخچه آمار ربات (30 روز اخیر)**\n\n"
            
            # Show last 10 records
            for i, record in enumerate(stats_history[:10]):
                stat_date = record[1]  # stat_date column
                total_users = record[2]  # total_users column
                total_revenue = record[13]  # total_revenue column
                total_purchases = record[12]  # total_purchases column
                
                message += f"📅 **{stat_date}**\n"
                message += f"👥 کاربران: {total_users:,}\n"
                message += f"💰 درآمد: {total_revenue:,} تومان\n"
                message += f"🛒 خریدها: {total_purchases:,}\n\n"

            # Add trend analysis
            if len(stats_history) >= 2:
                latest = stats_history[0]
                previous = stats_history[1]
                
                user_change = latest[2] - previous[2]  # total_users
                revenue_change = latest[13] - previous[13]  # total_revenue
                
                message += "📈 **تغییرات از روز قبل:**\n"
                message += f"👥 کاربران: {user_change:+,}\n"
                message += f"💰 درآمد: {revenue_change:+,} تومان\n"

            keyboard = [
                [InlineKeyboardButton("📊 آمار تفصیلی", callback_data="detailed_stats_history")],
                [InlineKeyboardButton("📈 نمودار روند", callback_data="stats_trend_chart")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)
            bot_logger.log_admin_action(user_id, "VIEW_STATS_HISTORY", "Statistics history viewed")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_stats_history")

    async def show_detailed_stats_history(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show detailed statistics history"""
        try:
            stats_history = await db_manager.get_bot_stats_history(7)  # Last 7 days
            
            if not stats_history:
                await update.callback_query.edit_message_text("📊 **آمار تفصیلی**\n\nهنوز آماری ثبت نشده است.")
                return

            message = "📊 **آمار تفصیلی (7 روز اخیر)**\n\n"
            
            for record in stats_history:
                stat_date = record[1]
                total_users = record[2]
                active_users_30d = record[3]
                active_users_7d = record[4]
                active_users_1d = record[5]
                new_users_7d = record[6]
                users_with_purchases = record[7]
                banned_users = record[8]
                total_apple_ids = record[9]
                available_apple_ids = record[10]
                sold_apple_ids = record[11]
                total_purchases = record[12]
                total_revenue = record[13]
                total_wallet_balance = record[15]
                pending_deposits = record[16]
                
                message += f"📅 **{stat_date}**\n"
                message += f"👥 کل کاربران: {total_users:,}\n"
                message += f"🟢 فعال (30روز): {active_users_30d:,}\n"
                message += f"🟡 فعال (7روز): {active_users_7d:,}\n"
                message += f"🔴 فعال (1روز): {active_users_1d:,}\n"
                message += f"🆕 کاربران جدید: {new_users_7d:,}\n"
                message += f"🛒 کاربران خریدار: {users_with_purchases:,}\n"
                message += f"🚫 کاربران بن: {banned_users:,}\n"
                message += f"📱 کل اکانت: {total_apple_ids:,}\n"
                message += f"✅ موجود: {available_apple_ids:,}\n"
                message += f"❌ فروخته: {sold_apple_ids:,}\n"
                message += f"💰 درآمد: {total_revenue:,} تومان\n"
                message += f"💳 موجودی کل: {total_wallet_balance:,} تومان\n"
                message += f"⏳ واریز در انتظار: {pending_deposits:,}\n\n"

            keyboard = [
                [InlineKeyboardButton("🔙 بازگشت", callback_data="back_to_stats_history")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_detailed_stats_history")

    async def show_revenue_trends(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show revenue trends and analysis"""
        try:
            # Get revenue statistics from database
            import aiosqlite
            async with aiosqlite.connect(db_manager.sql_db_path) as db:
                cursor = await db.execute('''
                    SELECT period_type, period_start, period_end, total_revenue, total_orders
                    FROM revenue_stats 
                    ORDER BY created_at DESC 
                    LIMIT 20
                ''')
                revenue_data = await cursor.fetchall()

            if not revenue_data:
                await update.callback_query.edit_message_text("📈 **روند درآمد**\n\nهنوز آمار درآمدی ثبت نشده است.")
                return

            message = "📈 **تحلیل روند درآمد**\n\n"
            
            # Group by period type
            periods = {'monthly': [], 'quarterly': [], 'semi_annual': [], 'annual': []}
            
            for record in revenue_data:
                period_type, period_start, period_end, total_revenue, total_orders = record
                if period_type in periods:
                    periods[period_type].append({
                        'start': period_start,
                        'end': period_end,
                        'revenue': total_revenue,
                        'orders': total_orders
                    })

            # Show latest data for each period
            period_names = {
                'monthly': '1 ماه اخیر',
                'quarterly': '3 ماه اخیر', 
                'semi_annual': '6 ماه اخیر',
                'annual': '1 سال اخیر'
            }

            for period_type, period_name in period_names.items():
                if periods[period_type]:
                    latest = periods[period_type][0]
                    message += f"💰 **{period_name}:**\n"
                    message += f"• درآمد: {latest['revenue']:,} تومان\n"
                    message += f"• سفارشات: {latest['orders']:,}\n"
                    message += f"• میانگین هر سفارش: {latest['revenue']//latest['orders']:,} تومان\n\n" if latest['orders'] > 0 else "• میانگین هر سفارش: 0 تومان\n\n"

            # Calculate growth if we have multiple records
            if len(periods['monthly']) >= 2:
                current = periods['monthly'][0]
                previous = periods['monthly'][1]
                growth = ((current['revenue'] - previous['revenue']) / previous['revenue'] * 100) if previous['revenue'] > 0 else 0
                
                message += f"📊 **رشد ماهانه:** {growth:+.1f}%\n"

            keyboard = [
                [InlineKeyboardButton("🔙 بازگشت", callback_data="back_to_stats_history")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_revenue_trends")


# Create service instance
stats_history_service = StatsHistoryService()
