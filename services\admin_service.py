from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from datetime import datetime
import shutil
import os
from logger import bot_logger, logger
from database_manager import db_manager
from error_handler import error_handler
from models.purchase_plans import purchase_plans_model
from models.wallet import wallet_model
from services.backup_service import backup_service
from services.plan_management_service import plan_management_service
from services.user_management_service import user_management_service
from services.settings_service import settings_service
from messages import *

class AdminService:
    def __init__(self):
        logger.info("AdminService initialized successfully")
    
    async def show_bot_stats(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show bot statistics to admin"""
        try:
            logger.info("Admin requested bot stats")

            # Get statistics from Excel database
            all_apple_ids = db_manager.get_all_apple_ids()
            available_ids = db_manager.get_available_apple_ids()

            # Get statistics from SQL database
            import aiosqlite
            async with aiosqlite.connect(db_manager.sql_db_path) as database:
                # User statistics
                cursor = await database.execute("SELECT COUNT(*) FROM users")
                total_users = (await cursor.fetchone())[0]

                # Purchase statistics - combine both single orders and plan orders
                # Single orders
                cursor = await database.execute("SELECT COUNT(*), SUM(price) FROM single_orders WHERE status = 'completed'")
                single_data = await cursor.fetchone()
                single_purchases = single_data[0] or 0
                single_revenue = single_data[1] or 0

                # Plan orders
                cursor = await database.execute("SELECT COUNT(*), SUM(price) FROM plan_orders WHERE status = 'completed'")
                plan_data = await cursor.fetchone()
                plan_purchases = plan_data[0] or 0
                plan_revenue = plan_data[1] or 0

                # Legacy purchases table
                cursor = await database.execute("SELECT COUNT(*), SUM(total_price) FROM purchases")
                legacy_data = await cursor.fetchone()
                legacy_purchases = legacy_data[0] or 0
                legacy_revenue = legacy_data[1] or 0

                # Total statistics
                total_purchases = single_purchases + plan_purchases + legacy_purchases
                total_revenue = single_revenue + plan_revenue + legacy_revenue

                # Wallet statistics
                cursor = await database.execute("SELECT SUM(wallet_balance) FROM users")
                total_wallet_balance = (await cursor.fetchone())[0] or 0

                # Pending deposits
                cursor = await database.execute("SELECT COUNT(*) FROM wallet_transactions WHERE transaction_type = 'deposit' AND status = 'pending'")
                pending_deposits = (await cursor.fetchone())[0] or 0

                # Additional statistics
                # Active users (users with activity in last 30 days)
                cursor = await database.execute("""
                    SELECT COUNT(*) FROM users
                    WHERE datetime(last_activity) >= datetime('now', '-30 days')
                """)
                active_users = (await cursor.fetchone())[0] or 0

                # New users (last 7 days)
                cursor = await database.execute("""
                    SELECT COUNT(*) FROM users
                    WHERE datetime(created_at) >= datetime('now', '-7 days')
                """)
                new_users_week = (await cursor.fetchone())[0] or 0

                # Users with purchases
                cursor = await database.execute("SELECT COUNT(*) FROM users WHERE total_purchases > 0")
                users_with_purchases = (await cursor.fetchone())[0] or 0

                # Average purchase per user
                avg_purchase_per_user = total_purchases / total_users if total_users > 0 else 0

                # Banned users count
                cursor = await database.execute("SELECT COUNT(*) FROM banned_users WHERE is_active = 1")
                banned_users_count = (await cursor.fetchone())[0] or 0

                # Admin count
                cursor = await database.execute("SELECT COUNT(*) FROM admins WHERE is_active = 1")
                admin_count = (await cursor.fetchone())[0] or 0

                # Approved deposits
                cursor = await database.execute("SELECT COUNT(*), SUM(amount) FROM wallet_transactions WHERE transaction_type = 'deposit' AND status = 'approved'")
                approved_deposits_data = await cursor.fetchone()
                approved_deposits_count = approved_deposits_data[0] or 0
                approved_deposits_amount = approved_deposits_data[1] or 0

                # Time-based revenue statistics
                revenue_1_month = await db_manager.get_revenue_by_period(1)
                revenue_3_months = await db_manager.get_revenue_by_period(3)
                revenue_6_months = await db_manager.get_revenue_by_period(6)
                revenue_12_months = await db_manager.get_revenue_by_period(12)

                # Additional detailed statistics
                # Top users by purchases
                cursor = await database.execute('''
                    SELECT user_id, username, first_name, total_purchases, total_spent
                    FROM users WHERE total_purchases > 0
                    ORDER BY total_spent DESC LIMIT 5
                ''')
                top_users = await cursor.fetchall()

                # Recent activity statistics
                cursor = await database.execute('''
                    SELECT COUNT(*) FROM users
                    WHERE datetime(last_activity) >= datetime('now', '-1 days')
                ''')
                daily_active_users = (await cursor.fetchone())[0] or 0

                cursor = await database.execute('''
                    SELECT COUNT(*) FROM users
                    WHERE datetime(last_activity) >= datetime('now', '-7 days')
                ''')
                weekly_active_users = (await cursor.fetchone())[0] or 0

                # Purchase trends (last 30 days)
                cursor = await database.execute('''
                    SELECT COUNT(*) FROM single_orders
                    WHERE status = 'completed' AND datetime(created_at) >= datetime('now', '-30 days')
                ''')
                recent_single_orders = (await cursor.fetchone())[0] or 0

                cursor = await database.execute('''
                    SELECT COUNT(*) FROM plan_orders
                    WHERE status = 'completed' AND datetime(created_at) >= datetime('now', '-30 days')
                ''')
                recent_plan_orders = (await cursor.fetchone())[0] or 0

                # Wallet statistics
                cursor = await database.execute('''
                    SELECT AVG(wallet_balance) FROM users WHERE wallet_balance > 0
                ''')
                avg_wallet_balance = (await cursor.fetchone())[0] or 0

                cursor = await database.execute('''
                    SELECT MAX(wallet_balance) FROM users
                ''')
                max_wallet_balance = (await cursor.fetchone())[0] or 0

                # Failed transactions
                cursor = await database.execute('''
                    SELECT COUNT(*) FROM wallet_transactions WHERE status = 'rejected'
                ''')
                rejected_deposits = (await cursor.fetchone())[0] or 0

                # Domain distribution
                domain_distribution = {}
                for apple_id in all_apple_ids:
                    email = apple_id.get('Email')
                    if email:
                        domain = db_manager.extract_domain_from_email(str(email))
                        domain_distribution[domain] = domain_distribution.get(domain, 0) + 1

                # Most popular domains
                sorted_domains = sorted(domain_distribution.items(), key=lambda x: x[1], reverse=True)
                top_domains = sorted_domains[:5] if sorted_domains else []

            stats_message = BOT_STATS_HEADER

            # Apple ID statistics
            stats_message += APPLE_ID_STATS.format(
                total_count=len(all_apple_ids),
                available_count=len(available_ids),
                sold_count=len(all_apple_ids) - len(available_ids)
            )

            # User statistics
            stats_message += USER_STATS.format(
                total_users=total_users,
                active_users=active_users,
                new_users_week=new_users_week,
                users_with_purchases=users_with_purchases,
                banned_users_count=banned_users_count,
                avg_purchase_per_user=avg_purchase_per_user
            )

            # Purchase statistics
            stats_message += SALES_STATS.format(
                total_purchases=total_purchases,
                total_revenue=total_revenue,
                revenue_1_month=revenue_1_month['total_revenue'],
                orders_1_month=revenue_1_month['total_orders'],
                revenue_3_months=revenue_3_months['total_revenue'],
                orders_3_months=revenue_3_months['total_orders'],
                revenue_6_months=revenue_6_months['total_revenue'],
                orders_6_months=revenue_6_months['total_orders'],
                revenue_12_months=revenue_12_months['total_revenue'],
                orders_12_months=revenue_12_months['total_orders']
            )

            # Wallet statistics
            stats_message += WALLET_STATS.format(
                total_balance=total_wallet_balance,
                pending_deposits=pending_deposits,
                approved_deposits_count=approved_deposits_count,
                approved_deposits_amount=approved_deposits_amount
            )

            # Admin statistics
            from messages import ADMIN_STATS, ACTIVITY_STATS, DETAILED_SALES_STATS, TOP_USERS_STATS, DOMAIN_STATS
            stats_message += ADMIN_STATS.format(admin_count=admin_count)

            # Activity statistics
            stats_message += ACTIVITY_STATS.format(
                daily_active_users=daily_active_users,
                weekly_active_users=weekly_active_users,
                active_users=active_users
            )

            # Detailed sales statistics
            stats_message += DETAILED_SALES_STATS.format(
                recent_single_orders=recent_single_orders,
                recent_plan_orders=recent_plan_orders,
                avg_wallet_balance=avg_wallet_balance,
                max_wallet_balance=max_wallet_balance,
                rejected_deposits=rejected_deposits
            )

            # Top users
            if top_users:
                top_users_list = ""
                for i, user in enumerate(top_users, 1):
                    user_id, username, first_name, total_purchases, total_spent = user
                    name = first_name or "نامشخص"
                    username_text = f"@{username}" if username else ""
                    top_users_list += f"{i}. {name} {username_text} - {total_purchases} خرید ({total_spent:,} تومان)\n"

                stats_message += TOP_USERS_STATS.format(top_users_list=top_users_list)

            # Top domains
            if top_domains:
                top_domains_list = ""
                for i, (domain, count) in enumerate(top_domains, 1):
                    percentage = (count / len(all_apple_ids)) * 100 if all_apple_ids else 0
                    top_domains_list += f"{i}. {domain}: {count} ({percentage:.1f}%)\n"

                stats_message += DOMAIN_STATS.format(top_domains_list=top_domains_list)

            # Save statistics to database for persistence
            stats_data = {
                'total_users': total_users,
                'active_users_30d': active_users,
                'active_users_7d': weekly_active_users,
                'active_users_1d': daily_active_users,
                'new_users_7d': new_users_week,
                'users_with_purchases': users_with_purchases,
                'banned_users': banned_users_count,
                'total_apple_ids': len(all_apple_ids),
                'available_apple_ids': len(available_ids),
                'sold_apple_ids': len(all_apple_ids) - len(available_ids),
                'total_purchases': total_purchases,
                'total_revenue': total_revenue,
                'avg_purchase_per_user': avg_purchase_per_user,
                'total_wallet_balance': total_wallet_balance,
                'pending_deposits': pending_deposits,
                'approved_deposits': approved_deposits_count,
                'rejected_deposits': rejected_deposits,
                'avg_wallet_balance': avg_wallet_balance,
                'max_wallet_balance': max_wallet_balance,
                'admin_count': admin_count
            }

            # Save to database (async operation, don't wait for it)
            import asyncio
            asyncio.create_task(db_manager.save_bot_stats(stats_data))

            # Also save revenue statistics
            from datetime import datetime
            current_date = datetime.now().isoformat()

            # Save revenue stats for different periods
            asyncio.create_task(db_manager.save_revenue_stats('monthly', revenue_1_month['start_date'], revenue_1_month['end_date'], revenue_1_month))
            asyncio.create_task(db_manager.save_revenue_stats('quarterly', revenue_3_months['start_date'], revenue_3_months['end_date'], revenue_3_months))
            asyncio.create_task(db_manager.save_revenue_stats('semi_annual', revenue_6_months['start_date'], revenue_6_months['end_date'], revenue_6_months))
            asyncio.create_task(db_manager.save_revenue_stats('annual', revenue_12_months['start_date'], revenue_12_months['end_date'], revenue_12_months))

            keyboard = [
                [InlineKeyboardButton("🔄 بروزرسانی", callback_data="refresh_bot_stats")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(stats_message, parse_mode='Markdown', reply_markup=reply_markup)
            bot_logger.log_admin_action(update.effective_user.id, "VIEW_STATS", "Bot statistics viewed")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_bot_stats")

    async def refresh_bot_stats(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Refresh bot statistics - same format as show_bot_stats"""
        try:
            await update.callback_query.answer("🔄 آمار به‌روزرسانی شد", show_alert=False)

            # Get the same statistics as show_bot_stats but handle callback properly
            logger.info("Admin requested bot stats refresh")

            # Get Apple ID statistics
            all_apple_ids = db_manager.get_all_apple_ids()
            available_apple_ids = db_manager.get_available_apple_ids()

            total_apple_ids = len(all_apple_ids)
            available_count = len(available_apple_ids)
            sold_count = total_apple_ids - available_count

            # Get user statistics
            users = db_manager.get_all_users()
            total_users = len(users)

            # Calculate active users (30 days)
            from datetime import datetime, timedelta
            thirty_days_ago = datetime.now() - timedelta(days=30)
            seven_days_ago = datetime.now() - timedelta(days=7)
            one_day_ago = datetime.now() - timedelta(days=1)

            active_users_30d = 0
            active_users_7d = 0
            active_users_1d = 0
            new_users_7d = 0
            users_with_purchases = 0
            banned_users = 0

            for user in users:
                last_activity = user.get('last_activity')
                first_join = user.get('first_join_date')
                total_purchases = user.get('total_purchases', 0)

                # Parse dates if they're strings
                if isinstance(last_activity, str):
                    try:
                        last_activity = datetime.fromisoformat(last_activity.replace('Z', '+00:00'))
                    except:
                        last_activity = None

                if isinstance(first_join, str):
                    try:
                        first_join = datetime.fromisoformat(first_join.replace('Z', '+00:00'))
                    except:
                        first_join = None

                # Count active users
                if last_activity:
                    if last_activity >= thirty_days_ago:
                        active_users_30d += 1
                    if last_activity >= seven_days_ago:
                        active_users_7d += 1
                    if last_activity >= one_day_ago:
                        active_users_1d += 1

                # Count new users
                if first_join and first_join >= seven_days_ago:
                    new_users_7d += 1

                # Count users with purchases
                if total_purchases > 0:
                    users_with_purchases += 1

            # Get purchase statistics
            purchases = db_manager.get_all_purchases()
            total_purchases = len(purchases)
            total_revenue = sum(purchase.get('amount', 0) for purchase in purchases)
            avg_purchase_per_user = total_revenue / max(total_users, 1)

            # Get wallet statistics
            wallet_transactions = db_manager.get_all_wallet_transactions()
            total_wallet_balance = sum(user.get('wallet_balance', 0) for user in users)

            pending_deposits = sum(1 for tx in wallet_transactions if tx.get('status') == 'pending')
            approved_deposits = sum(1 for tx in wallet_transactions if tx.get('status') == 'approved')
            rejected_deposits = sum(1 for tx in wallet_transactions if tx.get('status') == 'rejected')

            approved_amount = sum(tx.get('amount', 0) for tx in wallet_transactions if tx.get('status') == 'approved')
            avg_wallet_balance = total_wallet_balance / max(total_users, 1)
            max_wallet_balance = max((user.get('wallet_balance', 0) for user in users), default=0)

            # Get admin count
            all_admins = self.auth_utils.get_all_admins()
            admin_count = len(all_admins)

            # Calculate revenue by time periods
            now = datetime.now()

            # 1 month revenue
            one_month_ago = now - timedelta(days=30)
            revenue_1_month = {'total': 0, 'count': 0, 'start_date': one_month_ago.strftime('%Y-%m-%d'), 'end_date': now.strftime('%Y-%m-%d')}

            # 3 months revenue
            three_months_ago = now - timedelta(days=90)
            revenue_3_months = {'total': 0, 'count': 0, 'start_date': three_months_ago.strftime('%Y-%m-%d'), 'end_date': now.strftime('%Y-%m-%d')}

            # 6 months revenue
            six_months_ago = now - timedelta(days=180)
            revenue_6_months = {'total': 0, 'count': 0, 'start_date': six_months_ago.strftime('%Y-%m-%d'), 'end_date': now.strftime('%Y-%m-%d')}

            # 12 months revenue
            twelve_months_ago = now - timedelta(days=365)
            revenue_12_months = {'total': 0, 'count': 0, 'start_date': twelve_months_ago.strftime('%Y-%m-%d'), 'end_date': now.strftime('%Y-%m-%d')}

            for purchase in purchases:
                purchase_date = purchase.get('purchase_date')
                amount = purchase.get('amount', 0)

                if isinstance(purchase_date, str):
                    try:
                        purchase_date = datetime.fromisoformat(purchase_date.replace('Z', '+00:00'))
                    except:
                        continue
                elif not isinstance(purchase_date, datetime):
                    continue

                if purchase_date >= one_month_ago:
                    revenue_1_month['total'] += amount
                    revenue_1_month['count'] += 1
                if purchase_date >= three_months_ago:
                    revenue_3_months['total'] += amount
                    revenue_3_months['count'] += 1
                if purchase_date >= six_months_ago:
                    revenue_6_months['total'] += amount
                    revenue_6_months['count'] += 1
                if purchase_date >= twelve_months_ago:
                    revenue_12_months['total'] += amount
                    revenue_12_months['count'] += 1

            # Count single vs plan purchases
            single_purchases = sum(1 for p in purchases if p.get('purchase_type') == 'single')
            plan_purchases = sum(1 for p in purchases if p.get('purchase_type') == 'plan')

            # Domain statistics
            domain_stats = {}
            for apple_id in all_apple_ids:
                email = apple_id.get('email', '')
                if '@' in email:
                    domain = email.split('@')[1]
                    domain_stats[domain] = domain_stats.get(domain, 0) + 1

            # Build statistics message
            stats_message = f"""📊 آمار کامل ربات

📱 Apple ID ها:
• تعداد کل: {total_apple_ids}
• موجود: {available_count}
• فروخته شده: {sold_count}

👥 کاربران:
• تعداد کل کاربران: {total_users}
• کاربران فعال (30 روز اخیر): {active_users_30d}
• کاربران جدید (7 روز اخیر): {new_users_7d}
• کاربران با خرید: {users_with_purchases}
• کاربران بن شده: {banned_users}
• میانگین خرید هر کاربر: {avg_purchase_per_user:.1f}

💰 فروش:
• تعداد خریدها: {total_purchases}
• درآمد کل: {total_revenue:,} تومان

📊 درآمد زمان‌بندی شده:
• 1 ماه اخیر: {revenue_1_month['total']:,} تومان ({revenue_1_month['count']} سفارش)
• 3 ماه اخیر: {revenue_3_months['total']:,} تومان ({revenue_3_months['count']} سفارش)
• 6 ماه اخیر: {revenue_6_months['total']:,} تومان ({revenue_6_months['count']} سفارش)
• 1 سال اخیر: {revenue_12_months['total']:,} تومان ({revenue_12_months['count']} سفارش)

💳 کیف پول:
• موجودی کل کاربران: {total_wallet_balance:,} تومان
• تراکنش‌های در انتظار: {pending_deposits}
• واریزهای تایید شده: {approved_deposits} ({approved_amount:,} تومان)

👑 مدیریت:
• تعداد ادمین‌ها: {admin_count}

📈 فعالیت کاربران:
• فعال امروز: {active_users_1d}
• فعال این هفته: {active_users_7d}
• فعال این ماه: {active_users_30d}

📊 جزئیات فروش (30 روز اخیر):
• سفارشات تکی: {single_purchases}
• سفارشات پلنی: {plan_purchases}

💳 آمار کیف پول:
• میانگین موجودی: {avg_wallet_balance:,.0f} تومان
• بیشترین موجودی: {max_wallet_balance:,} تومان
• واریزهای رد شده: {rejected_deposits}

🌐 محبوب‌ترین دامنه‌ها:"""

            if domain_stats:
                sorted_domains = sorted(domain_stats.items(), key=lambda x: x[1], reverse=True)[:3]
                for i, (domain, count) in enumerate(sorted_domains, 1):
                    percentage = (count / total_apple_ids) * 100
                    stats_message += f"\n{i}. {domain}: {count} ({percentage:.1f}%)"

            keyboard = [
                [InlineKeyboardButton("🔄 بروزرسانی", callback_data="refresh_bot_stats")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(stats_message, parse_mode='Markdown', reply_markup=reply_markup)

            bot_logger.log_admin_action(update.effective_user.id, "REFRESH_STATS", "Bot statistics refreshed")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "refresh_bot_stats")
    
    async def show_apple_id_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show Apple ID management options"""
        try:
            logger.info("Admin accessed Apple ID management")

            all_apple_ids = db_manager.get_all_apple_ids()

            if not all_apple_ids:
                await update.message.reply_text(NO_APPLE_IDS_IN_DB)
                return

            # Count available and sold accounts
            available_count = 0
            sold_count = 0

            for apple_id in all_apple_ids:
                is_available = not apple_id.get('Status') or str(apple_id.get('Status', '')).strip() == ''
                if is_available:
                    available_count += 1
                else:
                    sold_count += 1

            # Count domains
            domain_stats = {}
            for apple_id in all_apple_ids:
                email = apple_id.get('Email')
                if email:
                    domain = db_manager.extract_domain_from_email(str(email))
                    domain_stats[domain] = domain_stats.get(domain, 0) + 1

            message = "📱 **مدیریت Apple ID ها**\n\n"
            message += f"📊 **خلاصه آمار:**\n"
            message += f"• مجموع اکانت‌ها: {len(all_apple_ids)} عدد\n"
            message += f"• اکانت‌های موجود: {available_count} عدد\n"
            message += f"• اکانت‌های فروخته شده: {sold_count} عدد\n\n"

            if domain_stats:
                message += "🌐 **آمار دامنه‌ها:**\n"
                # Sort by count (descending) and show top 5
                sorted_domains = sorted(domain_stats.items(), key=lambda x: x[1], reverse=True)
                for domain, count in sorted_domains[:5]:
                    message += f"• {domain}: {count} عدد\n"

                if len(sorted_domains) > 5:
                    message += f"• ... و {len(sorted_domains) - 5} دامنه دیگر\n"

            keyboard = [
                [InlineKeyboardButton("🔄 بروزرسانی", callback_data="refresh_apple_ids")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)
            bot_logger.log_admin_action(update.effective_user.id, "VIEW_APPLE_ID_MGMT", "Apple ID management accessed")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_apple_id_management")

    async def refresh_apple_ids(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Refresh Apple ID management"""
        try:
            await update.callback_query.answer("🔄 اطلاعات به‌روزرسانی شد", show_alert=False)

            # Get fresh data
            all_apple_ids = db_manager.get_all_apple_ids()

            if not all_apple_ids:
                await update.callback_query.edit_message_text(NO_APPLE_IDS_IN_DB)
                return

            # Count available and sold accounts
            available_count = 0
            sold_count = 0

            for apple_id in all_apple_ids:
                is_available = not apple_id.get('Status') or str(apple_id.get('Status', '')).strip() == ''
                if is_available:
                    available_count += 1
                else:
                    sold_count += 1

            # Count domains
            domain_stats = {}
            for apple_id in all_apple_ids:
                email = apple_id.get('Email')
                if email:
                    domain = db_manager.extract_domain_from_email(str(email))
                    domain_stats[domain] = domain_stats.get(domain, 0) + 1

            message = "📱 **مدیریت Apple ID ها**\n\n"
            message += f"📊 **خلاصه آمار:**\n"
            message += f"• مجموع اکانت‌ها: {len(all_apple_ids)} عدد\n"
            message += f"• اکانت‌های موجود: {available_count} عدد\n"
            message += f"• اکانت‌های فروخته شده: {sold_count} عدد\n\n"

            if domain_stats:
                message += "🌐 **آمار دامنه‌ها:**\n"
                # Sort by count (descending) and show top 5
                sorted_domains = sorted(domain_stats.items(), key=lambda x: x[1], reverse=True)
                for domain, count in sorted_domains[:5]:
                    message += f"• {domain}: {count} عدد\n"

                if len(sorted_domains) > 5:
                    message += f"• ... و {len(sorted_domains) - 5} دامنه دیگر\n"

            keyboard = [
                [InlineKeyboardButton("🔄 بروزرسانی", callback_data="refresh_apple_ids")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Add timestamp to ensure message is different
            from datetime import datetime
            timestamp = datetime.now().strftime("%H:%M:%S")
            message += f"\n🕐 آخرین بروزرسانی: {timestamp}"

            await update.callback_query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            bot_logger.log_admin_action(update.effective_user.id, "REFRESH_APPLE_ID_MGMT", "Apple ID management refreshed")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "refresh_apple_ids")

    async def show_plans_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show plans management interface"""
        try:
            await plan_management_service.show_plans_management(update, context)
        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_plans_management")
    
    async def show_user_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show user management options"""
        try:
            await user_management_service.show_user_management(update, context)
        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_user_management")
    
    async def show_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show bot settings"""
        try:
            await settings_service.show_settings_menu(update, context)
        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_settings")
    
    async def show_logs(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show recent logs to admin"""
        try:
            import os
            from dotenv import load_dotenv

            # Load environment variables to get log file name
            load_dotenv()
            log_file = os.getenv('LOG_FILE', 'botlog.txt')

            if not os.path.exists(log_file):
                await update.message.reply_text(f"📋 فایل لاگ یافت نشد: {log_file}")
                return

            # Read last 25 lines of log file
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                recent_logs = lines[-25:] if len(lines) > 25 else lines

            if not recent_logs:
                await update.message.reply_text("📋 فایل لاگ خالی است")
                return

            log_message = f"📋 **آخرین لاگ ها ({log_file}):**\n\n```\n"
            log_message += "".join(recent_logs)
            log_message += "\n```"

            # Split message if too long for Telegram
            if len(log_message) > 4000:
                log_message = log_message[:3900] + "...\n\n[لاگ کامل در فایل موجود است]\n```"

            await update.message.reply_text(log_message, parse_mode='Markdown')
            bot_logger.log_admin_action(update.effective_user.id, "VIEW_LOGS", f"Logs viewed from {log_file}")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_logs")
    
    async def show_pending_deposits(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show all pending transactions (deposits, single orders, plan orders)"""
        try:
            # Get pending deposits
            pending_deposits = await wallet_model.get_pending_deposits()

            # Get pending single orders
            from services.single_purchase_service import single_purchase_service
            pending_single_orders = await single_purchase_service.get_all_pending_orders()

            # Get pending plan orders
            from services.purchase_service import purchase_service
            pending_plan_orders = await purchase_service.get_all_pending_plan_orders()

            # Check if any pending transactions exist
            total_pending = len(pending_deposits) + len(pending_single_orders) + len(pending_plan_orders)

            if total_pending == 0:
                await update.message.reply_text(NO_PENDING_DEPOSITS)
                return

            message = PENDING_DEPOSITS_HEADER

            # Add pending deposits
            if pending_deposits:
                message += "💰 **واریزهای در انتظار:**\n\n"
                for deposit in pending_deposits:
                    user_name = f"{deposit.get('first_name', '')} {deposit.get('last_name', '')}".strip()
                    if not user_name:
                        user_name = "نام نامشخص"

                    username = f"@{deposit['username']}" if deposit.get('username') else "بدون نام کاربری"

                    message += f"""👤 **کاربر:** {user_name} ({username})
🆔 **ID:** `{deposit['user_id']}`
💵 **مبلغ:** {deposit['amount']:,} تومان
🆔 **شماره تراکنش:** {deposit['id']}
📅 **تاریخ:** {deposit['created_at'][:16].replace('T', ' ')}
──────────────────────────────

"""

            # Add pending single orders
            if pending_single_orders:
                message += "🛒 **خریدهای تکی در انتظار:**\n\n"
                for order in pending_single_orders:
                    user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
                    if not user_name:
                        user_name = "نام نامشخص"

                    username = f"@{order['username']}" if order.get('username') else "بدون نام کاربری"

                    message += f"""👤 **کاربر:** {user_name} ({username})
🆔 **ID:** `{order['user_id']}`
🛒 **نوع:** خرید تکی
📧 **دامنه:** {order['domain']}
💵 **قیمت:** {order['price']:,} تومان
🆔 **شماره سفارش:** {order['order_id']}
📅 **تاریخ:** {order['created_at'][:16]}
──────────────────────────────

"""

            # Add pending plan orders
            if pending_plan_orders:
                message += "📦 **خریدهای پلن در انتظار:**\n\n"
                for order in pending_plan_orders:
                    user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
                    if not user_name:
                        user_name = "نام نامشخص"

                    username = f"@{order['username']}" if order.get('username') else "بدون نام کاربری"

                    message += f"""👤 **کاربر:** {user_name} ({username})
🆔 **ID:** `{order['user_id']}`
📦 **نوع:** خرید پلن
📧 **پلن:** {order['plan_name']}
🌐 **دامنه:** {order['domain']}
🔢 **تعداد:** {order['quantity']} عدد
💵 **قیمت:** {order['price']:,} تومان
🆔 **شماره سفارش:** {order['order_id']}
📅 **تاریخ:** {order['created_at'][:16]}
──────────────────────────────

"""

            # Add glass buttons for individual transactions
            keyboard = []

            # Add pending deposits as individual buttons
            if pending_deposits:
                for deposit in pending_deposits:
                    user_name = f"{deposit.get('first_name', '')} {deposit.get('last_name', '')}".strip()
                    if not user_name:
                        user_name = "نام نامشخص"

                    button_text = f"💰 تراکنش {deposit['transaction_id']} - {user_name}"
                    keyboard.append([InlineKeyboardButton(button_text, callback_data=f"view_deposit_{deposit['transaction_id']}")])

            # Add pending single orders as individual buttons
            if pending_single_orders:
                for order in pending_single_orders:
                    user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
                    if not user_name:
                        user_name = "نام نامشخص"

                    button_text = f"🛒 سفارش {order['order_id']} - {user_name}"
                    keyboard.append([InlineKeyboardButton(button_text, callback_data=f"view_single_order_{order['order_id']}")])

            # Add pending plan orders as individual buttons
            if pending_plan_orders:
                for order in pending_plan_orders:
                    user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
                    if not user_name:
                        user_name = "نام نامشخص"

                    button_text = f"📦 پلن {order['order_id']} - {user_name}"
                    keyboard.append([InlineKeyboardButton(button_text, callback_data=f"view_plan_order_{order['order_id']}")])

            # No back button needed - admins can use admin menu navigation

            reply_markup = InlineKeyboardMarkup(keyboard) if keyboard else None

            await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)
            bot_logger.log_admin_action(update.effective_user.id, "VIEW_PENDING_TRANSACTIONS", f"Viewed {total_pending} pending transactions")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_pending_transactions")

    async def view_deposit_details(self, update: Update, context: ContextTypes.DEFAULT_TYPE, transaction_id: int) -> None:
        """Show deposit details with approve/reject buttons"""
        try:
            deposit = await wallet_model.get_transaction_by_id(transaction_id)

            if not deposit:
                await update.callback_query.answer("❌ تراکنش یافت نشد.", show_alert=True)
                return

            user_name = f"{deposit.get('first_name', '')} {deposit.get('last_name', '')}".strip()
            if not user_name:
                user_name = "نام نامشخص"

            username = f"@{deposit['username']}" if deposit.get('username') else "بدون نام کاربری"

            message = f"""💰 **جزئیات تراکنش واریز**

👤 **کاربر:** {user_name}
🆔 **نام کاربری:** {username}
📱 **شناسه تلگرام:** {deposit['user_id']}

💳 **جزئیات تراکنش:**
• شماره تراکنش: `{deposit['transaction_id']}`
• مبلغ: {deposit['amount']:,} تومان
• تاریخ: {deposit['created_at'][:16]}

⏳ **وضعیت:** در انتظار تایید"""

            keyboard = [
                [
                    InlineKeyboardButton("✅ تایید", callback_data=f"approve_deposit_{transaction_id}"),
                    InlineKeyboardButton("❌ رد", callback_data=f"reject_deposit_{transaction_id}")
                ],
                [InlineKeyboardButton("🔙 بازگشت", callback_data="pending_deposits")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "view_deposit_details")

    async def manage_single_orders(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Manage pending single orders with glass buttons"""
        try:
            from services.single_purchase_service import single_purchase_service
            pending_orders = await single_purchase_service.get_all_pending_orders()

            if not pending_orders:
                await update.callback_query.edit_message_text("✅ هیچ سفارش تکی در انتظار وجود ندارد.")
                return

            message = "🛒 **مدیریت خریدهای تکی در انتظار**\n\n"
            keyboard = []

            for order in pending_orders:
                user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
                if not user_name:
                    user_name = "نام نامشخص"

                # Create glass button for each order
                button_text = f"🔸 {user_name} - {order['domain']} ({order['price']:,}ت)"
                keyboard.append([InlineKeyboardButton(button_text, callback_data=f"view_single_order_{order['order_id']}")])

            keyboard.append([InlineKeyboardButton("🔙 بازگشت", callback_data="show_pending_single_orders")])
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "manage_single_orders")

    async def manage_plan_orders(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Manage pending plan orders with glass buttons"""
        try:
            from services.purchase_service import purchase_service
            pending_orders = await purchase_service.get_all_pending_plan_orders()

            if not pending_orders:
                await update.callback_query.edit_message_text("✅ هیچ سفارش پلن در انتظار وجود ندارد.")
                return

            message = "📦 **مدیریت خریدهای پلن در انتظار**\n\n"
            keyboard = []

            for order in pending_orders:
                user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
                if not user_name:
                    user_name = "نام نامشخص"

                # Create glass button for each order
                button_text = f"🔸 {user_name} - {order['plan_name']} ({order['price']:,}ت)"
                keyboard.append([InlineKeyboardButton(button_text, callback_data=f"view_plan_order_{order['order_id']}")])

            keyboard.append([InlineKeyboardButton("🔙 بازگشت", callback_data="show_pending_plan_orders")])
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "manage_plan_orders")

    async def view_single_order_details(self, update: Update, context: ContextTypes.DEFAULT_TYPE, order_id: str) -> None:
        """Show single order details with approve/reject buttons"""
        try:
            from services.single_purchase_service import single_purchase_service
            order = await single_purchase_service.get_order_by_id(order_id)

            if not order:
                await update.callback_query.answer("❌ سفارش یافت نشد.", show_alert=True)
                return

            user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
            if not user_name:
                user_name = "نام نامشخص"

            username = f"@{order['username']}" if order.get('username') else "بدون نام کاربری"

            message = f"""🛒 **جزئیات سفارش تکی**

👤 **کاربر:** {user_name}
🆔 **نام کاربری:** {username}
📱 **شناسه تلگرام:** {order['user_id']}

🌐 **دامنه:** {order['domain']}
💰 **قیمت:** {order['price']:,} تومان
📅 **تاریخ:** {order['created_at'][:16]}

⏳ **وضعیت:** در انتظار تایید"""

            keyboard = [
                [
                    InlineKeyboardButton("✅ تایید", callback_data=f"approve_single_order_{order_id}"),
                    InlineKeyboardButton("❌ رد", callback_data=f"reject_single_order_{order_id}")
                ],
                [InlineKeyboardButton("🔙 بازگشت", callback_data="manage_single_orders")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "view_single_order_details")

    async def view_plan_order_details(self, update: Update, context: ContextTypes.DEFAULT_TYPE, order_id: str) -> None:
        """Show plan order details with approve/reject buttons"""
        try:
            from services.purchase_service import purchase_service
            order = await purchase_service.get_plan_order_by_id(order_id)

            if not order:
                await update.callback_query.answer("❌ سفارش یافت نشد.", show_alert=True)
                return

            user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
            if not user_name:
                user_name = "نام نامشخص"

            username = f"@{order['username']}" if order.get('username') else "بدون نام کاربری"

            message = f"""📦 **جزئیات سفارش پلن**

👤 **کاربر:** {user_name}
🆔 **نام کاربری:** {username}
📱 **شناسه تلگرام:** {order['user_id']}

📋 **پلن:** {order['plan_name']}
🌐 **دامنه:** {order['domain']}
🔢 **تعداد:** {order['quantity']} عدد
💰 **قیمت:** {order['price']:,} تومان
📅 **تاریخ:** {order['created_at'][:16]}

⏳ **وضعیت:** در انتظار تایید"""

            keyboard = [
                [
                    InlineKeyboardButton("✅ تایید", callback_data=f"approve_plan_order_{order_id}"),
                    InlineKeyboardButton("❌ رد", callback_data=f"reject_plan_order_{order_id}")
                ],
                [InlineKeyboardButton("🔙 بازگشت", callback_data="manage_plan_orders")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "view_plan_order_details")

    async def manual_backup(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Perform manual backup"""
        try:
            # Import backup_service here to avoid circular import issues
            from services.backup_service import backup_service

            if backup_service:
                await update.message.reply_text(MANUAL_BACKUP_START)

                json_path, sql_path, excel_paths = await backup_service.manual_backup()

                # Send backup files
                await backup_service.send_backup_files_to_admin(json_path, sql_path, "manual", excel_paths)

                await update.message.reply_text(MANUAL_BACKUP_SUCCESS)
                bot_logger.log_admin_action(update.effective_user.id, "MANUAL_BACKUP", "Manual backup completed")
            else:
                # Try to create backup manually if service is not available
                await self.create_manual_backup_fallback(update, context)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "manual_backup")

    async def create_manual_backup_fallback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Fallback method for manual backup when service is unavailable"""
        try:
            await update.message.reply_text("📁 در حال ایجاد بکاپ (حالت fallback)...")

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Create JSON backup
            json_backup_path = await db_manager.backup_to_json()

            # Create SQL backup
            backup_dir = "backups"
            os.makedirs(backup_dir, exist_ok=True)
            sql_backup_filename = f"database_backup_{timestamp}.db"
            sql_backup_path = os.path.join(backup_dir, sql_backup_filename)
            shutil.copy2(db_manager.sql_db_path, sql_backup_path)

            # Create Excel backups
            excel_backup_paths = []
            database_dir = "database"
            if os.path.exists(database_dir):
                import glob
                excel_files = glob.glob(os.path.join(database_dir, "*.xlsx"))
                for excel_file in excel_files:
                    if os.path.exists(excel_file):
                        original_name = os.path.basename(excel_file)
                        name_without_ext = os.path.splitext(original_name)[0]
                        backup_filename = f"{name_without_ext}_backup_{timestamp}.xlsx"
                        backup_path = os.path.join(backup_dir, backup_filename)
                        shutil.copy2(excel_file, backup_path)
                        excel_backup_paths.append(backup_path)

            # Send files to admin
            user_id = update.effective_user.id

            message = f"📁 **بکاپ دستی**\n\n📅 تاریخ: {datetime.now().strftime('%Y/%m/%d %H:%M')}\n🆔 شناسه: {timestamp}\n\n"
            message += f"📄 **فایل‌های ضمیمه:**\n• 📄 دیتابیس JSON\n• 🗄️ دیتابیس SQL\n"
            if excel_backup_paths:
                message += f"• 📊 فایل‌های اکسل ({len(excel_backup_paths)} فایل)\n"
            message += f"\n✅ بکاپ با موفقیت ایجاد شد"

            await update.message.reply_text(message, parse_mode='Markdown')

            # Send JSON file
            if os.path.exists(json_backup_path):
                with open(json_backup_path, 'rb') as json_file:
                    await context.bot.send_document(
                        chat_id=user_id,
                        document=json_file,
                        filename=os.path.basename(json_backup_path),
                        caption="📄 بکاپ JSON دیتابیس"
                    )

            # Send SQL file
            if os.path.exists(sql_backup_path):
                with open(sql_backup_path, 'rb') as sql_file:
                    await context.bot.send_document(
                        chat_id=user_id,
                        document=sql_file,
                        filename=os.path.basename(sql_backup_path),
                        caption="🗄️ بکاپ SQL دیتابیس"
                    )

            # Send Excel backup files
            for excel_path in excel_backup_paths:
                if os.path.exists(excel_path):
                    with open(excel_path, 'rb') as excel_file:
                        await context.bot.send_document(
                            chat_id=user_id,
                            document=excel_file,
                            filename=os.path.basename(excel_path),
                            caption="📊 بکاپ فایل اکسل"
                        )

            bot_logger.log_admin_action(user_id, "MANUAL_BACKUP_FALLBACK", f"Manual backup fallback completed: {timestamp}")

        except Exception as e:
            logger.error(f"Manual backup fallback failed: {str(e)}")
            await update.message.reply_text("❌ خطا در ایجاد بکاپ")

    async def refresh_data(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Refresh database data"""
        try:
            # Reinitialize database connections
            db_manager.initialize_excel_database()
            await db_manager.initialize_sql_database()

            await update.message.reply_text(DATABASE_REFRESHED)
            bot_logger.log_admin_action(update.effective_user.id, "REFRESH_DATA", "Databases refreshed")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "refresh_data")

    async def show_accounts_from_database(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show accounts from database - admin can specify range"""
        try:
            message = """📱 **نمایش اکانت‌ها از دیتابیس**

📋 **راهنما:**
• برای نمایش یک اکانت: عدد تکی (مثال: `35`)
• برای نمایش چند اکانت: محدوده (مثال: `30-45`)

⚠️ **نکته:** اگر تعداد بیش از 10 باشد، به صورت فایل txt ارسال می‌شود.

🔢 **لطفاً شماره یا محدوده مورد نظر را وارد کنید:**

❌ **برای لغو:** دستور `/cancel` را ارسال کنید"""

            context.user_data['waiting_for_account_range'] = True
            await update.message.reply_text(message, parse_mode='Markdown')

            bot_logger.log_admin_action(
                update.effective_user.id,
                "REQUEST_ACCOUNT_DISPLAY",
                "Admin requested account display from database"
            )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_accounts_from_database")

    async def process_account_range(self, update: Update, context: ContextTypes.DEFAULT_TYPE, range_text: str) -> None:
        """Process account range request from admin"""
        try:
            context.user_data.pop('waiting_for_account_range', None)

            # Parse range
            if '-' in range_text:
                # Range format: 30-45
                try:
                    start, end = map(int, range_text.split('-'))
                    if start > end:
                        start, end = end, start
                except ValueError:
                    await update.message.reply_text("❌ فرمت نامعتبر. مثال صحیح: `30-45`", parse_mode='Markdown')
                    return
            else:
                # Single number: 35
                try:
                    start = end = int(range_text)
                except ValueError:
                    await update.message.reply_text("❌ فرمت نامعتبر. مثال صحیح: `35`", parse_mode='Markdown')
                    return

            # Get all Apple IDs from database
            all_apple_ids = db_manager.get_all_apple_ids()

            if not all_apple_ids:
                await update.message.reply_text("❌ هیچ اکانتی در دیتابیس یافت نشد.")
                return

            # Validate range
            if start < 1 or end > len(all_apple_ids):
                await update.message.reply_text(f"❌ محدوده نامعتبر. تعداد کل اکانت‌ها: {len(all_apple_ids)}")
                return

            # Get requested accounts (convert to 0-based index)
            requested_accounts = all_apple_ids[start-1:end]
            count = len(requested_accounts)

            if count > 10:
                # Send as file
                await self.send_accounts_as_file(update, context, requested_accounts, start, end)
            else:
                # Send as formatted message
                await self.send_accounts_as_message(update, context, requested_accounts, start, end)

            bot_logger.log_admin_action(
                update.effective_user.id,
                "VIEW_ACCOUNTS_RANGE",
                f"Viewed accounts {start}-{end} ({count} accounts)"
            )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "process_account_range")

    async def send_accounts_as_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE, accounts: list, start: int, end: int) -> None:
        """Send accounts as formatted message"""
        try:
            message = f"📱 **اکانت‌های {start} تا {end}**\n\n"

            for i, account in enumerate(accounts, start):
                status_raw = account.get('Status')
                if status_raw is None:
                    status = ''
                else:
                    status = str(status_raw).strip()
                status_emoji = "🔴" if status.lower() in ['s', 'sold', 'فروخته شده'] else "🟢"

                message += f"{status_emoji} **اکانت #{i}:**\n"
                message += f"ایمیل: `{account.get('Email', account.get('email', 'N/A'))}`\n"
                message += f"پسورد: `{account.get('Password', account.get('pass', 'N/A'))}`\n"
                message += f"وضعیت: `{status or 'موجود'}`\n"

                # Security questions
                q1 = account.get('Q1', account.get('q1', ''))
                q2 = account.get('Q2', account.get('q2', ''))
                q3 = account.get('Q3', account.get('q3', ''))

                if q1:
                    message += f"سوال امنیتی 1: `{q1}`\n"
                if q2:
                    message += f"سوال امنیتی 2: `{q2}`\n"
                if q3:
                    message += f"سوال امنیتی 3: `{q3}`\n"

                # Creation date
                date = account.get('Date', account.get('date', ''))
                if date:
                    message += f"تاریخ ایجاد: `{date}`\n"

                # Purchase information if sold
                if status.lower() in ['s', 'sold', 'فروخته شده']:
                    first_name = account.get('first_name', account.get('First Name', ''))
                    last_name = account.get('last_name', account.get('Last Name', ''))
                    telegram_id = account.get('telegram_id', account.get('Telegram ID', ''))
                    purchase_date = account.get('purchase_date', account.get('Purchase Date', ''))
                    purchase_time = account.get('time', account.get('Time', ''))

                    if first_name or last_name:
                        message += f"خریدار: `{first_name} {last_name}`\n"
                    if telegram_id:
                        message += f"تلگرام: `@{telegram_id}`\n"
                    if purchase_date:
                        message += f"تاریخ خرید: `{purchase_date}`\n"
                    if purchase_time:
                        message += f"زمان خرید: `{purchase_time}`\n"
                message += "──────────────────────────────\n"

            await update.message.reply_text(message, parse_mode='Markdown')

        except Exception as e:
            await error_handler.handle_error(update, context, e, "send_accounts_as_message")

    async def send_accounts_as_file(self, update: Update, context: ContextTypes.DEFAULT_TYPE, accounts: list, start: int, end: int) -> None:
        """Send accounts as txt file"""
        try:
            filename = f"accounts_{start}_{end}.txt"
            filepath = f"temp/{filename}"

            # Create temp directory if not exists
            os.makedirs("temp", exist_ok=True)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"اکانت‌های {start} تا {end}\n")
                f.write("=" * 50 + "\n\n")

                for i, account in enumerate(accounts, start):
                    status_raw = account.get('Status')
                    if status_raw is None:
                        status = ''
                    else:
                        status = str(status_raw).strip()
                    status_text = "فروخته شده" if status.lower() in ['s', 'sold', 'فروخته شده'] else "موجود"

                    f.write(f"اکانت #{i}:\n")
                    f.write(f"ایمیل: {account.get('Email', account.get('email', 'N/A'))}\n")
                    f.write(f"پسورد: {account.get('Password', account.get('password', 'N/A'))}\n")

                    # Add security questions
                    q1 = account.get('Q1', account.get('q1', ''))
                    q2 = account.get('Q2', account.get('q2', ''))
                    q3 = account.get('Q3', account.get('q3', ''))

                    if q1:
                        f.write(f"سوال امنیتی 1: {q1}\n")
                    if q2:
                        f.write(f"سوال امنیتی 2: {q2}\n")
                    if q3:
                        f.write(f"سوال امنیتی 3: {q3}\n")

                    # Creation date
                    date = account.get('Date', account.get('date', ''))
                    if date:
                        f.write(f"تاریخ ایجاد: {date}\n")

                    f.write(f"وضعیت: {status_text}\n")

                    if status.lower() in ['s', 'sold', 'فروخته شده']:
                        f.write(f"خریدار: {account.get('first_name', 'N/A')} {account.get('last_name', '')}\n")
                        f.write(f"تلگرام: @{account.get('telegram_id', 'N/A')}\n")
                        f.write(f"تاریخ خرید: {account.get('purchase_date', 'N/A')}\n")
                        f.write(f"زمان خرید: {account.get('time', 'N/A')}\n")

                    f.write("-" * 30 + "\n\n")

            # Send file
            with open(filepath, 'rb') as f:
                await update.message.reply_document(
                    document=f,
                    filename=filename,
                    caption=f"📱 **اکانت‌های {start} تا {end}**\n\n📊 تعداد: {len(accounts)} اکانت"
                )

            # Clean up
            os.remove(filepath)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "send_accounts_as_file")
