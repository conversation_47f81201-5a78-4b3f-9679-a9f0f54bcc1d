from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
import aiosqlite
from datetime import datetime
from logger import bot_logger, logger
from error_handler import error_handler
from utils.auth import AuthUtils
from database_manager import db_manager


class UserSearchService:
    def __init__(self):
        self.auth_utils = AuthUtils()

    async def show_user_search_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show user search prompt"""
        try:
            user_id = update.effective_user.id
            
            # Only master admin can search users
            if not self.auth_utils.is_master_admin(user_id):
                await update.message.reply_text("❌ فقط ادمین اصلی می‌تواند کاربران را جست‌وجو کند")
                return

            message = """🔍 **جست‌وجوی کاربر**

لطفاً آیدی عددی تلگرامی کاربری که می‌خواهید جست‌وجو کنید را ارسال کنید.

💡 **نکته:** آیدی عددی کاربر را می‌توانید از بخش مدیریت کاربران یا از پیام‌های کاربر دریافت کنید.

❌ برای لغو عملیات، دستور /cancel را ارسال کنید."""

            await update.message.reply_text(message, parse_mode='Markdown')
            
            # Set state for waiting user ID
            context.user_data['waiting_user_search'] = True
            
            bot_logger.log_admin_action(user_id, "USER_SEARCH_PROMPT", "User search prompt shown")
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_user_search_prompt")

    async def process_user_search(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user_id_text: str) -> None:
        """Process user search by ID"""
        try:
            admin_id = update.effective_user.id
            
            # Validate user ID
            try:
                search_user_id = int(user_id_text.strip())
            except ValueError:
                await update.message.reply_text(
                    "❌ آیدی عددی نامعتبر است. لطفاً یک عدد صحیح ارسال کنید."
                )
                return

            # Search for user
            user_data = await self.get_user_complete_info(search_user_id)
            
            if not user_data:
                await update.message.reply_text(
                    f"❌ کاربری با آیدی `{search_user_id}` در ربات یافت نشد.",
                    parse_mode='Markdown'
                )
                return

            # Display user information
            await self.display_user_info(update, context, user_data)
            
            # Clear search state
            context.user_data.pop('waiting_user_search', None)
            
            bot_logger.log_admin_action(admin_id, "USER_SEARCH", f"Searched for user {search_user_id}")
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "process_user_search")

    async def get_user_complete_info(self, user_id: int) -> dict:
        """Get complete user information"""
        try:
            async with aiosqlite.connect(db_manager.sql_db_path) as db:
                # Get basic user info
                cursor = await db.execute('''
                    SELECT user_id, username, first_name, last_name, language_code, 
                           wallet_balance, total_purchases, total_spent, 
                           first_join_date, last_activity, created_at
                    FROM users WHERE user_id = ?
                ''', (user_id,))
                user_data = await cursor.fetchone()
                
                if not user_data:
                    return None

                # Get purchase history
                cursor = await db.execute('''
                    SELECT COUNT(*), SUM(total_price) FROM purchases WHERE user_id = ?
                ''', (user_id,))
                legacy_purchases = await cursor.fetchone()

                cursor = await db.execute('''
                    SELECT COUNT(*), SUM(price) FROM single_orders WHERE user_id = ? AND status = 'completed'
                ''', (user_id,))
                single_purchases = await cursor.fetchone()

                cursor = await db.execute('''
                    SELECT COUNT(*), SUM(price) FROM plan_orders WHERE user_id = ? AND status = 'completed'
                ''', (user_id,))
                plan_purchases = await cursor.fetchone()

                # Get wallet transactions
                cursor = await db.execute('''
                    SELECT COUNT(*) FROM wallet_transactions WHERE user_id = ? AND status = 'approved'
                ''', (user_id,))
                approved_deposits = await cursor.fetchone()

                cursor = await db.execute('''
                    SELECT COUNT(*) FROM wallet_transactions WHERE user_id = ? AND status = 'pending'
                ''', (user_id,))
                pending_deposits = await cursor.fetchone()

                # Check if user is banned
                is_banned = await db_manager.is_user_banned(user_id)

                return {
                    'basic_info': user_data,
                    'legacy_purchases': legacy_purchases,
                    'single_purchases': single_purchases,
                    'plan_purchases': plan_purchases,
                    'approved_deposits': approved_deposits[0] if approved_deposits else 0,
                    'pending_deposits': pending_deposits[0] if pending_deposits else 0,
                    'is_banned': is_banned
                }

        except Exception as e:
            logger.error(f"Failed to get user info for {user_id}: {str(e)}")
            return None

    async def display_user_info(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user_data: dict) -> None:
        """Display complete user information"""
        try:
            basic_info = user_data['basic_info']
            user_id, username, first_name, last_name, language_code, wallet_balance, total_purchases, total_spent, first_join_date, last_activity, created_at = basic_info

            # Format name
            name = f"{first_name or ''} {last_name or ''}".strip() or "نامشخص"
            username_text = f"@{username}" if username else "بدون نام کاربری"

            # Calculate total purchases and revenue
            legacy_count = user_data['legacy_purchases'][0] or 0
            legacy_revenue = user_data['legacy_purchases'][1] or 0
            single_count = user_data['single_purchases'][0] or 0
            single_revenue = user_data['single_purchases'][1] or 0
            plan_count = user_data['plan_purchases'][0] or 0
            plan_revenue = user_data['plan_purchases'][1] or 0

            total_purchase_count = legacy_count + single_count + plan_count
            total_revenue = legacy_revenue + single_revenue + plan_revenue

            # Ban status
            ban_status = "🚫 بن شده" if user_data['is_banned'] else "✅ فعال"

            message = f"""👤 **اطلاعات کامل کاربر**

📋 **مشخصات اصلی:**
🆔 **آیدی:** `{user_id}`
👤 **نام:** {name}
📱 **نام کاربری:** {username_text}
🌐 **زبان:** {language_code or 'نامشخص'}
📅 **تاریخ عضویت:** {first_join_date[:10] if first_join_date else 'نامشخص'}
⏰ **آخرین فعالیت:** {last_activity[:10] if last_activity else 'نامشخص'}
🔒 **وضعیت:** {ban_status}

💰 **اطلاعات مالی:**
💳 **موجودی کیف پول:** {wallet_balance:,} تومان
💸 **کل مبلغ خرج شده:** {total_revenue:,} تومان

🛒 **تاریخچه خریدها:**
📦 **تعداد کل خریدها:** {total_purchase_count}
• خریدهای قدیمی: {legacy_count} ({legacy_revenue:,} تومان)
• خریدهای تکی: {single_count} ({single_revenue:,} تومان)
• خریدهای پلنی: {plan_count} ({plan_revenue:,} تومان)

💳 **تراکنش‌های کیف پول:**
✅ **واریزهای تایید شده:** {user_data['approved_deposits']}
⏳ **واریزهای در انتظار:** {user_data['pending_deposits']}

🔧 **عملیات مدیریتی:**"""

            # Create management buttons
            keyboard = [
                [InlineKeyboardButton("🗑️ پاکسازی خریدها", callback_data=f"clear_purchases_{user_id}")],
                [InlineKeyboardButton("📱 پاکسازی اکانت‌ها", callback_data=f"clear_accounts_{user_id}")],
                [InlineKeyboardButton("💰 تغییر موجودی کیف پول", callback_data=f"change_wallet_{user_id}")],
            ]

            if user_data['is_banned']:
                keyboard.append([InlineKeyboardButton("✅ رفع بن کاربر", callback_data=f"unban_user_{user_id}")])
            else:
                keyboard.append([InlineKeyboardButton("🚫 بن کاربر", callback_data=f"ban_user_{user_id}")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "display_user_info")


# Create service instance
user_search_service = UserSearchService()
