from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
import aiosqlite
from datetime import datetime
from typing import Dict, List, Optional

from logger import logger, bot_logger
from error_handler import error_handler
from utils.auth import AuthUtils
from database_manager import db_manager
from messages import *

class AdminManagementService:
    def __init__(self):
        self.auth_utils = AuthUtils()
        self.sql_db_path = db_manager.sql_db_path
        logger.info("AdminManagementService initialized successfully")
    
    async def show_admin_management_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show admin management main menu"""
        try:
            user_id = update.effective_user.id

            # Clear any pending states
            context.user_data.pop('adding_admin', None)
            context.user_data.pop('removing_admin', None)

            # Only master admin can manage other admins
            if not self.auth_utils.is_master_admin(user_id):
                if update.callback_query:
                    await update.callback_query.answer(
                        "❌ فقط ادمین اصلی می‌تواند ادمین‌های دیگر را مدیریت کند",
                        show_alert=True
                    )
                else:
                    await update.message.reply_text(
                        "❌ فقط ادمین اصلی می‌تواند ادمین‌های دیگر را مدیریت کند"
                    )
                return
            
            # Get all admins
            all_admins = await self.auth_utils.get_all_admins()
            master_admins = [admin for admin in all_admins if admin['type'] == 'master']
            additional_admins = [admin for admin in all_admins if admin['type'] == 'additional']

            # Show master admin IDs
            master_ids_text = ", ".join([f"`{admin['user_id']}`" for admin in master_admins])

            message = f"""👑 **مدیریت ادمین‌ها**

🔧 **ادمین‌های اصلی:** {master_ids_text}

📊 **آمار ادمین‌ها:**
• ادمین‌های اصلی: {len(master_admins)}
• ادمین‌های اضافی: {len(additional_admins)}
• مجموع: {len(all_admins)}

⚠️ **توجه:**
• شما ادمین اصلی هستید
• ادمین‌های اصلی قابل حذف نیستند
• ادمین‌های اضافی نمی‌توانند ادمین‌های دیگر را مدیریت کنند

🔽 **عملیات موجود:**"""

            keyboard = [
                [InlineKeyboardButton("➕ اضافه کردن ادمین جدید", callback_data="add_new_admin")],
                [InlineKeyboardButton("📋 لیست ادمین‌ها", callback_data="list_all_admins")],
                [InlineKeyboardButton("🗑️ حذف ادمین", callback_data="remove_admin_menu")]
            ]
            
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            if update.callback_query:
                await update.callback_query.edit_message_text(
                    message, 
                    reply_markup=reply_markup,
                    parse_mode='Markdown'
                )
            else:
                await update.message.reply_text(
                    message, 
                    reply_markup=reply_markup,
                    parse_mode='Markdown'
                )
                
            bot_logger.log_admin_action(user_id, "ADMIN_MANAGEMENT_MENU", "Viewed admin management menu")
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_admin_management_menu")
    
    async def start_add_admin_process(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Start the process of adding a new admin"""
        try:
            user_id = update.effective_user.id
            
            # Only master admin can add other admins
            if not self.auth_utils.is_master_admin(user_id):
                await update.callback_query.answer(
                    "❌ فقط ادمین اصلی می‌تواند ادمین جدید اضافه کند", 
                    show_alert=True
                )
                return
            
            message = """➕ **اضافه کردن ادمین جدید**

📝 **راهنما:**
• آیدی عددی کاربر مورد نظر را ارسال کنید
• آیدی عددی باید یک عدد صحیح باشد
• کاربر باید قبلاً از ربات استفاده کرده باشد

💡 **نکته:** برای دریافت آیدی عددی، کاربر می‌تواند دستور `/start` را در ربات ارسال کند

🔢 **آیدی عددی کاربر را ارسال کنید:**"""

            keyboard = [
                [InlineKeyboardButton("❌ لغو", callback_data="admin_management_menu")]
            ]
            
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            # Set user state
            context.user_data['adding_admin'] = True
            
            await update.callback_query.edit_message_text(
                message, 
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
            bot_logger.log_admin_action(user_id, "START_ADD_ADMIN", "Started adding new admin process")
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "start_add_admin_process")
    
    async def process_add_admin(self, update: Update, context: ContextTypes.DEFAULT_TYPE, admin_id_text: str) -> None:
        """Process adding a new admin"""
        try:
            user_id = update.effective_user.id
            
            # Only master admin can add other admins
            if not self.auth_utils.is_master_admin(user_id):
                await update.message.reply_text("❌ فقط ادمین اصلی می‌تواند ادمین جدید اضافه کند")
                return
            
            # Validate admin ID
            try:
                new_admin_id = int(admin_id_text.strip())
            except ValueError:
                await update.message.reply_text(
                    "❌ آیدی عددی نامعتبر است. لطفاً یک عدد صحیح ارسال کنید."
                )
                return
            
            # Check if user is trying to add themselves
            if new_admin_id == user_id:
                await update.message.reply_text("❌ شما نمی‌توانید خودتان را به عنوان ادمین اضافه کنید.")
                return
            
            # Check if user is already master admin
            if new_admin_id == self.auth_utils.get_admin_id():
                await update.message.reply_text("❌ این کاربر ادمین اصلی است.")
                return
            
            # Check if user is already an admin (active or inactive)
            async with aiosqlite.connect(self.sql_db_path) as db:
                # First check if admin already exists (active or inactive)
                cursor = await db.execute(
                    "SELECT is_active FROM admins WHERE user_id = ?",
                    (new_admin_id,)
                )
                admin_result = await cursor.fetchone()

                if admin_result:
                    if admin_result[0] == 1:  # Active admin
                        await update.message.reply_text("❌ این کاربر قبلاً ادمین فعال است.")
                        return
                    else:  # Inactive admin - reactivate
                        await db.execute(
                            "UPDATE admins SET is_active = 1, updated_at = ? WHERE user_id = ?",
                            (datetime.now().isoformat(), new_admin_id)
                        )
                        await db.commit()

                        # Get user data for success message
                        cursor = await db.execute(
                            "SELECT username, first_name, last_name FROM users WHERE user_id = ?",
                            (new_admin_id,)
                        )
                        user_data = await cursor.fetchone()

                        if user_data:
                            username_display = f"@{user_data[0]}" if user_data[0] else "بدون نام کاربری"
                            name_display = f"{user_data[1] or ''} {user_data[2] or ''}".strip() or "بدون نام"

                            message = f"""✅ **ادمین مجدداً فعال شد**

👤 **اطلاعات ادمین:**
🆔 **آیدی عددی:** `{new_admin_id}`
👤 **نام کاربری:** {username_display}
📝 **نام:** {name_display}
⏰ **زمان فعال‌سازی:** {datetime.now().strftime('%Y/%m/%d %H:%M:%S')}

✅ **این کاربر مجدداً دسترسی ادمین دارد.**"""

                            await update.message.reply_text(message, parse_mode='Markdown')

                            # Send notification to reactivated admin
                            await self.notify_admin_reactivated(context, new_admin_id, user_data[0], user_data[1], user_data[2])

                            bot_logger.log_admin_action(
                                user_id,
                                "REACTIVATE_ADMIN_SUCCESS",
                                f"Reactivated admin: {new_admin_id} ({username_display})"
                            )

                        # Clear user state
                        context.user_data.pop('adding_admin', None)
                        return

                # Check if user exists in users table
                cursor = await db.execute(
                    "SELECT username, first_name, last_name FROM users WHERE user_id = ?",
                    (new_admin_id,)
                )
                user_data = await cursor.fetchone()

                if not user_data:
                    await update.message.reply_text(
                        "❌ کاربر با این آیدی در ربات یافت نشد. کاربر باید ابتدا از ربات استفاده کند."
                    )
                    return

                # Add new admin
                try:
                    await db.execute('''
                        INSERT INTO admins (user_id, username, first_name, last_name, added_by, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        new_admin_id,
                        user_data[0],  # username
                        user_data[1],  # first_name
                        user_data[2],  # last_name
                        user_id,       # added_by
                        datetime.now().isoformat(),
                        datetime.now().isoformat()
                    ))

                    await db.commit()

                except Exception as db_error:
                    logger.error(f"Database error adding admin {new_admin_id}: {str(db_error)}")
                    await update.message.reply_text(
                        "❌ خطا در اضافه کردن ادمین. لطفاً دوباره تلاش کنید."
                    )
                    return
            
            # Clear user state
            context.user_data.pop('adding_admin', None)
            
            # Success message
            username_display = f"@{user_data[0]}" if user_data[0] else "بدون نام کاربری"
            name_display = f"{user_data[1] or ''} {user_data[2] or ''}".strip() or "بدون نام"
            
            message = f"""✅ **ادمین جدید با موفقیت اضافه شد**

👤 **اطلاعات ادمین:**
🆔 **آیدی عددی:** `{new_admin_id}`
👤 **نام کاربری:** {username_display}
📝 **نام:** {name_display}
⏰ **زمان اضافه شدن:** {datetime.now().strftime('%Y/%m/%d %H:%M:%S')}

✅ **این کاربر اکنون دسترسی ادمین دارد.**"""

            await update.message.reply_text(message, parse_mode='Markdown')

            # Send notification to new admin
            await self.notify_admin_added(context, new_admin_id, user_data[0], user_data[1], user_data[2])

            bot_logger.log_admin_action(
                user_id,
                "ADD_ADMIN_SUCCESS",
                f"Added new admin: {new_admin_id} ({username_display})"
            )
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "process_add_admin")

    async def notify_admin_reactivated(self, context, admin_id: int, username: str, first_name: str, last_name: str):
        """Send notification to reactivated admin"""
        try:
            username_display = f"@{username}" if username else "بدون نام کاربری"
            name_display = f"{first_name or ''} {last_name or ''}".strip() or "بدون نام"

            message = f"""🎉 **تبریک! شما مجدداً ادمین شدید!**

👤 **اطلاعات شما:**
🆔 **آیدی عددی:** `{admin_id}`
👤 **نام کاربری:** {username_display}
📝 **نام:** {name_display}
⏰ **زمان فعال‌سازی:** {datetime.now().strftime('%Y/%m/%d %H:%M:%S')}

✅ **شما اکنون مجدداً دسترسی ادمین دارید و می‌توانید از امکانات مدیریتی ربات استفاده کنید.**

🔄 **برای مشاهده پنل ادمین دستور /start را ارسال کنید.**"""

            await context.bot.send_message(
                chat_id=admin_id,
                text=message,
                parse_mode='Markdown'
            )

            logger.info(f"Reactivation notification sent to admin {admin_id}")

        except Exception as e:
            logger.error(f"Failed to send reactivation notification to admin {admin_id}: {str(e)}")

    async def notify_admin_added(self, context, admin_id: int, username: str, first_name: str, last_name: str):
        """Send notification to newly added admin"""
        try:
            username_display = f"@{username}" if username else "بدون نام کاربری"
            name_display = f"{first_name or ''} {last_name or ''}".strip() or "بدون نام"

            message = f"""🎉 **تبریک! شما ادمین شدید!**

👑 **اطلاع‌رسانی مهم:**
ادمین اصلی ربات، دسترسی ادمینی به شما اعطا کرده است.

👤 **اطلاعات شما:**
🆔 **آیدی عددی:** `{admin_id}`
👤 **نام کاربری:** {username_display}
📝 **نام:** {name_display}
⏰ **زمان اعطای دسترسی:** {datetime.now().strftime('%Y/%m/%d %H:%M:%S')}

✨ **امکانات جدید شما:**
• 📊 مشاهده آمار ربات
• 📱 مدیریت Apple ID ها
• 📤 آپلود فایل اکسل
• ➕ اضافه کردن اکانت تکی
• 💰 مدیریت واریزهای در انتظار
• 📢 ارسال پیام به همه کاربران
• 📋 مشاهده لاگ ها
• 📁 دریافت بکاپ دستی
• ⚙️ تنظیمات ربات
• 🔄 بروزرسانی دیتابیس

🔄 **برای مشاهده پنل ادمین، دستور /start را ارسال کنید.**

🎊 **خوش آمدید به تیم مدیریت!**"""

            await context.bot.send_message(
                chat_id=admin_id,
                text=message,
                parse_mode='Markdown'
            )

            # Refresh keyboard to admin panel
            from handlers.start_handler import StartHandler
            start_handler = StartHandler()
            await start_handler.refresh_user_keyboard(context, admin_id, "🎉 کیبورد شما به پنل ادمین تغییر کرد!")

            logger.info(f"Admin notification sent to {admin_id}")
            bot_logger.log_admin_action(admin_id, "ADMIN_NOTIFICATION_SENT", "New admin notification sent")

        except Exception as e:
            logger.error(f"Failed to send admin notification to {admin_id}: {str(e)}")
            # Don't raise exception as this is not critical
    
    async def show_admins_list(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show list of all admins"""
        try:
            user_id = update.effective_user.id
            
            # Only master admin can view admin list
            if not self.auth_utils.is_master_admin(user_id):
                if update.callback_query:
                    await update.callback_query.answer(
                        "❌ فقط ادمین اصلی می‌تواند لیست ادمین‌ها را مشاهده کند",
                        show_alert=True
                    )
                else:
                    await update.message.reply_text(
                        "❌ فقط ادمین اصلی می‌تواند لیست ادمین‌ها را مشاهده کند"
                    )
                return
            
            # Get all admins
            all_admins = await self.auth_utils.get_all_admins()

            message = "📋 **لیست تمام ادمین‌ها**\n\n"

            # Master admins (can be multiple)
            master_admins = [admin for admin in all_admins if admin['type'] == 'master']
            if master_admins:
                if len(master_admins) == 1:
                    message += f"👑 **ادمین اصلی:**\n🆔 `{master_admins[0]['user_id']}`\n\n"
                else:
                    message += f"👑 **ادمین‌های اصلی ({len(master_admins)}):**\n\n"
                    for i, admin in enumerate(master_admins, 1):
                        message += f"**{i}.** ادمین اصلی\n"
                        message += f"🆔 `{admin['user_id']}`\n\n"

            # Additional admins
            additional_admins = [admin for admin in all_admins if admin['type'] == 'additional']
            if additional_admins:
                message += f"👥 **ادمین‌های اضافی ({len(additional_admins)}):**\n\n"
                for i, admin in enumerate(additional_admins, 1):
                    username_display = f"@{admin['username']}" if admin['username'] else "بدون نام کاربری"
                    name_display = f"{admin['first_name'] or ''} {admin['last_name'] or ''}".strip() or "بدون نام"

                    message += f"**{i}.** {name_display}\n"
                    message += f"🆔 `{admin['user_id']}`\n"
                    message += f"👤 {username_display}\n"
                    if admin.get('created_at'):
                        message += f"📅 {admin['created_at'][:10]}\n"
                    message += "\n"
            else:
                message += "👥 **ادمین‌های اضافی:** هیچ ادمین اضافی وجود ندارد\n\n"

            message += f"📊 **مجموع:** {len(all_admins)} ادمین"

            keyboard = [
                [InlineKeyboardButton("🔙 بازگشت", callback_data="admin_management")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.callback_query.edit_message_text(
                message, 
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
            bot_logger.log_admin_action(user_id, "VIEW_ADMINS_LIST", f"Viewed list of {len(all_admins)} admins")
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_admins_list")

    async def show_remove_admin_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show menu for removing admins"""
        try:
            user_id = update.effective_user.id

            # Only master admin can remove other admins
            if not self.auth_utils.is_master_admin(user_id):
                if update.callback_query:
                    await update.callback_query.answer(
                        "❌ فقط ادمین اصلی می‌تواند ادمین‌های دیگر را حذف کند",
                        show_alert=True
                    )
                else:
                    await update.message.reply_text(
                        "❌ فقط ادمین اصلی می‌تواند ادمین‌های دیگر را حذف کند"
                    )
                return

            # Get additional admins only
            all_admins = await self.auth_utils.get_all_admins()
            additional_admins = [admin for admin in all_admins if admin['type'] == 'additional']

            if not additional_admins:
                message = """🗑️ **حذف ادمین**

❌ **هیچ ادمین اضافی برای حذف وجود ندارد**

فقط ادمین‌های اضافی قابل حذف هستند. ادمین اصلی قابل حذف نیست."""

                keyboard = []
            else:
                message = f"""🗑️ **حذف ادمین**

📋 **ادمین‌های قابل حذف ({len(additional_admins)}):**

⚠️ **توجه:** ادمین اصلی قابل حذف نیست

🔽 **ادمین مورد نظر را انتخاب کنید:**"""

                keyboard = []
                for admin in additional_admins:
                    username_display = f"@{admin['username']}" if admin['username'] else "بدون نام کاربری"
                    name_display = f"{admin['first_name'] or ''} {admin['last_name'] or ''}".strip() or "بدون نام"
                    button_text = f"🗑️ {name_display} ({admin['user_id']})"

                    keyboard.append([
                        InlineKeyboardButton(
                            button_text,
                            callback_data=f"confirm_remove_admin_{admin['user_id']}"
                        )
                    ])



            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

            bot_logger.log_admin_action(user_id, "REMOVE_ADMIN_MENU", f"Viewed remove admin menu - {len(additional_admins)} removable admins")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_remove_admin_menu")

    async def confirm_remove_admin(self, update: Update, context: ContextTypes.DEFAULT_TYPE, admin_id_to_remove: int) -> None:
        """Show confirmation for removing an admin"""
        try:
            user_id = update.effective_user.id

            # Only master admin can remove other admins
            if not self.auth_utils.is_master_admin(user_id):
                if update.callback_query:
                    await update.callback_query.answer(
                        "❌ فقط ادمین اصلی می‌تواند ادمین‌های دیگر را حذف کند",
                        show_alert=True
                    )
                else:
                    await update.message.reply_text(
                        "❌ فقط ادمین اصلی می‌تواند ادمین‌های دیگر را حذف کند"
                    )
                return

            # Get admin info
            async with aiosqlite.connect(self.sql_db_path) as db:
                cursor = await db.execute(
                    "SELECT username, first_name, last_name, created_at FROM admins WHERE user_id = ? AND is_active = 1",
                    (admin_id_to_remove,)
                )
                admin_data = await cursor.fetchone()

                if not admin_data:
                    await update.callback_query.answer("❌ ادمین یافت نشد", show_alert=True)
                    return

            username_display = f"@{admin_data[0]}" if admin_data[0] else "بدون نام کاربری"
            name_display = f"{admin_data[1] or ''} {admin_data[2] or ''}".strip() or "بدون نام"

            message = f"""⚠️ **تأیید حذف ادمین**

👤 **اطلاعات ادمین:**
📝 **نام:** {name_display}
🆔 **آیدی عددی:** `{admin_id_to_remove}`
👤 **نام کاربری:** {username_display}
📅 **تاریخ اضافه شدن:** {admin_data[3][:10] if admin_data[3] else 'نامشخص'}

❌ **آیا مطمئن هستید که می‌خواهید این ادمین را حذف کنید؟**

⚠️ **توجه:** این عمل غیرقابل بازگشت است."""

            keyboard = [
                [
                    InlineKeyboardButton("✅ بله، حذف کن", callback_data=f"execute_remove_admin_{admin_id_to_remove}"),
                    InlineKeyboardButton("❌ لغو", callback_data="remove_admin_menu")
                ]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

            bot_logger.log_admin_action(user_id, "CONFIRM_REMOVE_ADMIN", f"Confirming removal of admin {admin_id_to_remove}")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "confirm_remove_admin")

    async def execute_remove_admin(self, update: Update, context: ContextTypes.DEFAULT_TYPE, admin_id_to_remove: int) -> None:
        """Execute the removal of an admin"""
        try:
            user_id = update.effective_user.id

            # Only master admin can remove other admins
            if not self.auth_utils.is_master_admin(user_id):
                await update.callback_query.answer(
                    "❌ فقط ادمین اصلی می‌تواند ادمین‌های دیگر را حذف کند",
                    show_alert=True
                )
                return

            # Get admin info before removal
            async with aiosqlite.connect(self.sql_db_path) as db:
                cursor = await db.execute(
                    "SELECT username, first_name, last_name FROM admins WHERE user_id = ? AND is_active = 1",
                    (admin_id_to_remove,)
                )
                admin_data = await cursor.fetchone()

                if not admin_data:
                    await update.callback_query.answer("❌ ادمین یافت نشد", show_alert=True)
                    return

                # Remove admin (set is_active to 0)
                await db.execute(
                    "UPDATE admins SET is_active = 0, updated_at = ? WHERE user_id = ?",
                    (datetime.now().isoformat(), admin_id_to_remove)
                )

                await db.commit()

            username_display = f"@{admin_data[0]}" if admin_data[0] else "بدون نام کاربری"
            name_display = f"{admin_data[1] or ''} {admin_data[2] or ''}".strip() or "بدون نام"

            message = f"""✅ **ادمین با موفقیت حذف شد**

👤 **اطلاعات ادمین حذف شده:**
📝 **نام:** {name_display}
🆔 **آیدی عددی:** `{admin_id_to_remove}`
👤 **نام کاربری:** {username_display}
⏰ **زمان حذف:** {datetime.now().strftime('%Y/%m/%d %H:%M:%S')}

❌ **این کاربر دیگر دسترسی ادمین ندارد.**"""

            keyboard = []

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

            # Send notification to removed admin
            await self.notify_admin_removed(context, admin_id_to_remove, admin_data[1], admin_data[2], admin_data[3])

            bot_logger.log_admin_action(
                user_id,
                "REMOVE_ADMIN_SUCCESS",
                f"Removed admin: {admin_id_to_remove} ({username_display})"
            )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "execute_remove_admin")

    async def notify_admin_removed(self, context, admin_id: int, username: str, first_name: str, last_name: str):
        """Send notification to removed admin"""
        try:
            username_display = f"@{username}" if username else "بدون نام کاربری"
            name_display = f"{first_name or ''} {last_name or ''}".strip() or "بدون نام"

            message = f"""⚠️ **اطلاع‌رسانی مهم**

🔒 **دسترسی ادمینی شما لغو شد**
ادمین اصلی ربات، دسترسی ادمینی شما را لغو کرده است.

👤 **اطلاعات شما:**
🆔 **آیدی عددی:** `{admin_id}`
👤 **نام کاربری:** {username_display}
📝 **نام:** {name_display}
⏰ **زمان لغو دسترسی:** {datetime.now().strftime('%Y/%m/%d %H:%M:%S')}

📱 **تغییرات:**
• دیگر به پنل ادمین دسترسی ندارید
• کیبورد شما به حالت کاربر عادی تغییر کرده است
• امکانات مدیریتی از شما گرفته شده است

🔄 **برای مشاهده پنل کاربر عادی، دستور /start را ارسال کنید.**

ℹ️ **شما همچنان می‌توانید از ربات به عنوان کاربر عادی استفاده کنید.**"""

            await context.bot.send_message(
                chat_id=admin_id,
                text=message,
                parse_mode='Markdown'
            )

            # Refresh keyboard to regular user panel
            from handlers.start_handler import StartHandler
            start_handler = StartHandler()
            await start_handler.refresh_user_keyboard(context, admin_id, "🔄 کیبورد شما به حالت کاربر عادی تغییر کرد")

            logger.info(f"Admin removal notification sent to {admin_id}")
            bot_logger.log_admin_action(admin_id, "ADMIN_REMOVAL_NOTIFICATION_SENT", "Admin removal notification sent")

        except Exception as e:
            logger.error(f"Failed to send admin removal notification to {admin_id}: {str(e)}")
            # Don't raise exception as this is not critical

# Create global instance
admin_management_service = AdminManagementService()
