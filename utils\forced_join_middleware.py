"""
Forced Join Middleware
Handles checking user membership in forced join channels before allowing access to bot features
"""

from typing import Dict, Any, Optional
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ReplyKeyboardRemove
from telegram.ext import ContextTypes

from logger import logger
from services.forced_join_service import forced_join_service
from utils.auth import AuthUtils


class ForcedJoinMiddleware:
    def __init__(self):
        self.auth_utils = AuthUtils()
        logger.info("ForcedJoinMiddleware initialized successfully")

    async def check_user_access(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """
        Check if user has access to bot features based on forced join channels membership
        Returns True if user has access, False otherwise
        """
        try:
            user_id = update.effective_user.id

            # Skip check for admins
            if await self.auth_utils.is_admin(user_id):
                logger.debug(f"User {user_id} is admin, skipping forced join check")
                return True

            # Check if forced join is enabled
            if not await self.is_forced_join_enabled():
                logger.debug(f"Forced join is disabled, allowing access for user {user_id}")
                return True

            logger.info(f"Checking forced join channels membership for user {user_id}")

            # Check user membership in forced join channels
            membership_result = await forced_join_service.check_user_membership(context.bot, user_id)

            logger.info(f"Membership check result for user {user_id}: is_member_of_all={membership_result['is_member_of_all']}, missing_channels={len(membership_result['missing_channels'])}, total_channels={membership_result['total_channels']}")

            if membership_result['is_member_of_all']:
                logger.info(f"User {user_id} is member of all required channels, allowing access")
                return True

            # User is not member of all required channels
            logger.info(f"User {user_id} is missing membership in {len(membership_result['missing_channels'])} channels, blocking access")
            await self.show_join_required_message(update, context, membership_result['missing_channels'])
            return False

        except Exception as e:
            logger.error(f"Error checking user access for user {user_id}: {str(e)}")
            # Check if forced join is enabled first
            if await self.is_forced_join_enabled():
                # If forced join is enabled but we have an error, block access for security
                logger.warning(f"Blocking user {user_id} due to error in forced join check")
                await update.message.reply_text(
                    "❌ **خطا در بررسی دسترسی**\n\nلطفاً دوباره تلاش کنید یا با پشتیبانی تماس بگیرید.",
                    parse_mode='Markdown'
                )
                return False
            else:
                # If no forced join channels are configured, allow access
                return True

    async def show_join_required_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE, 
                                       missing_channels: list) -> None:
        """Show message requiring user to join channels"""
        try:
            # Remove current keyboard and send temporary message
            temp_message = None
            if update.message:
                temp_message = await update.message.reply_text(
                    "⏳ در حال بررسی دسترسی...",
                    reply_markup=ReplyKeyboardRemove()
                )

            if not missing_channels:
                return

            message = """🔒 **دسترسی محدود شده**

📢 **برای استفاده از ربات، ابتدا باید عضو کانال‌های زیر شوید:**

"""

            # Create inline keyboard with channel buttons
            keyboard = []
            for i, channel in enumerate(missing_channels, 1):
                channel_title = channel.get('channel_title', 'کانال')
                channel_id = channel['channel_id']
                
                # Add channel info to message
                if channel.get('channel_username'):
                    channel_link = f"@{channel['channel_username']}"
                    message += f"**{i}.** {channel_title}\n🔗 {channel_link}\n\n"
                    
                    # Create button with channel link
                    keyboard.append([InlineKeyboardButton(
                        f"📢 {channel_title}",
                        url=f"https://t.me/{channel['channel_username']}"
                    )])
                else:
                    # For channels without username, try to create invite link
                    message += f"**{i}.** {channel_title}\n🆔 `{channel_id}`\n\n"
                    
                    # Try to create invite link or use channel ID
                    try:
                        if channel_id.startswith('-100'):
                            # Convert to public link format if possible
                            keyboard.append([InlineKeyboardButton(
                                f"📢 {channel_title}",
                                url=f"https://t.me/c/{channel_id[4:]}/1"
                            )])
                        else:
                            keyboard.append([InlineKeyboardButton(
                                f"📢 {channel_title}",
                                url=f"https://t.me/{channel_id}"
                            )])
                    except:
                        # If we can't create a proper link, just show the channel info
                        pass

            message += """💡 **راهنما:**
• روی دکمه‌های بالا کلیک کنید تا وارد کانال‌ها شوید
• پس از عضویت در همه کانال‌ها، دکمه "عضو شدم" را بزنید
• ربات عضویت شما را بررسی خواهد کرد

⚠️ **توجه:** تا زمانی که عضو همه کانال‌ها نشوید، نمی‌توانید از ربات استفاده کنید"""

            # Add "I joined" button
            keyboard.append([InlineKeyboardButton("✅ عضو شدم", callback_data="check_membership")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            # Delete temporary message if it exists
            if temp_message:
                try:
                    await temp_message.delete()
                except:
                    pass

            if update.callback_query:
                await update.callback_query.edit_message_text(
                    message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            elif update.message:
                await update.message.reply_text(
                    message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

        except Exception as e:
            logger.error(f"Error showing join required message: {str(e)}")

    async def handle_membership_check(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle membership check when user clicks 'I joined' button"""
        try:
            user_id = update.effective_user.id

            # Check membership again
            membership_result = await forced_join_service.check_user_membership(context.bot, user_id)

            if membership_result['is_member_of_all']:
                # User is now member of all channels
                message = """✅ **تبریک! دسترسی تایید شد**

🎉 شما با موفقیت عضو همه کانال‌های مورد نیاز شدید

🤖 **اکنون می‌توانید از تمام امکانات ربات استفاده کنید**

💡 برای شروع، دستور /start را ارسال کنید یا از دکمه‌های زیر استفاده کنید"""

                await update.callback_query.edit_message_text(
                    message,
                    parse_mode='Markdown'
                )

                # Send welcome message with keyboard
                from handlers.start_handler import StartHandler
                from messages import WELCOME_USER_NEW

                start_handler = StartHandler()
                user = update.effective_user
                user_data = {
                    'user_id': user.id,
                    'username': user.username,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'language_code': user.language_code,
                    'is_bot': user.is_bot,
                    'is_premium': getattr(user, 'is_premium', False)
                }

                # Get user information for welcome message
                user_info = await start_handler._get_user_welcome_info(user_data)

                # Send welcome message with keyboard
                welcome_message = WELCOME_USER_NEW.format(
                    user_id=user.id,
                    full_name=user_info['full_name'],
                    join_date=user_info['join_date'],
                    last_activity=user_info['last_activity']
                )

                await update.callback_query.message.reply_text(
                    welcome_message,
                    parse_mode='Markdown',
                    reply_markup=start_handler._get_user_keyboard()
                )

            else:
                # User is still not member of all channels
                await update.callback_query.answer(
                    "❌ هنوز عضو همه کانال‌ها نشده‌اید. لطفاً ابتدا عضو شوید.",
                    show_alert=True
                )

                # Show the join required message again
                await self.show_join_required_message(update, context, membership_result['missing_channels'])

        except Exception as e:
            logger.error(f"Error handling membership check: {str(e)}")
            await update.callback_query.answer(
                "❌ خطا در بررسی عضویت. لطفاً دوباره تلاش کنید.",
                show_alert=True
            )

    async def is_forced_join_enabled(self) -> bool:
        """Check if forced join is enabled (has active channels)"""
        try:
            from database_manager import db_manager
            channels = await db_manager.get_forced_join_channels(active_only=True)
            return len(channels) > 0
        except Exception as e:
            logger.error(f"Error checking if forced join is enabled: {str(e)}")
            return False

# Create global instance
forced_join_middleware = ForcedJoinMiddleware()
