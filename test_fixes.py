#!/usr/bin/env python3
"""
Test script for the fixes applied to the Telegram bot
"""

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all services can be imported"""
    print("🧪 Testing service imports...")
    
    try:
        from services.stats_history_service import stats_history_service
        from services.user_management_service import user_management_service
        from services.admin_service import AdminService
        from services.database_management_service import database_management_service
        
        print("✅ All service imports successful")
        return True
        
    except Exception as e:
        print(f"❌ Service import failed: {str(e)}")
        return False

def test_callback_handlers():
    """Test callback handler imports"""
    print("🧪 Testing callback handler imports...")
    
    try:
        from handlers.callback_handler import CallbackHandler
        print("✅ Callback handler import successful")
        return True
        
    except Exception as e:
        print(f"❌ Callback handler import failed: {str(e)}")
        return False

async def test_database_connection():
    """Test database connection"""
    print("🧪 Testing database connection...")
    
    try:
        from database_manager import db_manager
        
        # Test SQL database initialization
        await db_manager.initialize_sql_database()
        print("✅ Database connection successful")
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Running fix validation tests...\n")
    
    tests = [
        ("Service Imports", test_imports),
        ("Callback Handlers", test_callback_handlers),
        ("Database Connection", test_database_connection)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*40}")
        print(f"Running: {test_name}")
        print('='*40)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = asyncio.run(test_func())
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
                
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {str(e)}")
    
    print(f"\n{'='*40}")
    print(f"TEST SUMMARY: {passed}/{total} tests passed")
    print('='*40)
    
    if passed == total:
        print("🎉 All fixes validated successfully!")
        print("\n📋 Fixed issues:")
        print("1. ✅ Stats history back buttons")
        print("2. ✅ Refresh bot stats method")
        print("3. ✅ Refresh Apple IDs duplicate message")
        print("4. ✅ User management callback handlers")
        print("5. ✅ Database management back button removed")
        print("6. ✅ Admin management back button")
        return True
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ Ready to test the bot with fixes!")
    else:
        print("\n❌ Please fix the remaining issues.")
        sys.exit(1)
