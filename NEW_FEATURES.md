# 🚀 قابلیت‌های جدید ربات فروش Apple ID

## 📋 خلاصه تغییرات

این آپدیت شامل قابلیت‌های جدید و بهبودهای مهمی برای ربات فروش Apple ID است که تجربه مدیریت و نظارت را بهبود می‌بخشد.

## ✨ قابلیت‌های جدید

### 1. 🔍 جست‌وجوی کاربر
- **دکمه جدید**: "🔍 جست و جوی کاربر" در پنل ادمین
- **قابلیت**: جست‌وجو بر اساس آیدی عددی تلگرام
- **نمایش اطلاعات کامل کاربر**:
  - مشخصات شخصی
  - موجودی کیف پول
  - تاریخچه خریدها
  - تراکنش‌های کیف پول
  - وضعیت بن

### 2. 🛠️ عملیات مدیریت کاربر
پس از جست‌وجوی کاربر، ادمین می‌تواند:
- **🗑️ پاکسازی خریدها**: حذف تمام تراکنش‌های خرید
- **📱 پاکسازی اکانت‌ها**: آزادسازی اکانت‌های خریداری شده
- **💰 تغییر موجودی کیف پول**: تنظیم مبلغ جدید
- **🚫 بن/آنبن کاربر**: مسدود یا رفع مسدودیت

### 3. 🚫 سیستم بن کاربر
- **جدول جدید**: `banned_users` در دیتابیس
- **Middleware**: بررسی خودکار وضعیت بن در تمام handlers
- **محدودیت کامل**: کاربران بن شده نمی‌توانند از هیچ قابلیتی استفاده کنند
- **پیام مناسب**: نمایش پیام اطلاع‌رسانی برای کاربران بن شده

### 4. 📊 آمار پیشرفته ربات
آمار جدید اضافه شده:
- **👥 کاربران**:
  - فعال امروز، این هفته، این ماه
  - کاربران جدید (7 روز اخیر)
  - کاربران با خرید
  - کاربران بن شده
  - میانگین خرید هر کاربر

- **💰 مالی**:
  - واریزهای تایید شده
  - واریزهای رد شده
  - میانگین و بیشترین موجودی کیف پول

- **🏆 برترین کاربران**: نمایش 5 کاربر برتر بر اساس خرید
- **🌐 محبوب‌ترین دامنه‌ها**: آمار دامنه‌های ایمیل

### 5. 💰 آمار درآمد زمان‌بندی شده
- **درآمد 1 ماه اخیر**
- **درآمد 3 ماه اخیر**
- **درآمد 6 ماه اخیر**
- **درآمد 1 سال اخیر**
- **ذخیره در دیتابیس**: جدول `revenue_stats`

### 6. 🖥️ آمار و اطلاعات سرور
دکمه جدید "🖥️ آمار و اطلاعات سرور" با اطلاعات:
- **💻 سیستم**: OS، معماری، hostname، uptime
- **🔧 منابع**: CPU، RAM، تعداد هسته‌ها
- **💾 دیسک**: فضای کل، استفاده شده، آزاد
- **🌐 شبکه**: بایت و بسته‌های ارسالی/دریافتی
- **🗄️ دیتابیس**: حجم فایل‌های دیتابیس
- **🐍 Python**: نسخه، PID، زمان شروع

### 7. 📈 تاریخچه آمار
- **دکمه جدید**: "📈 تاریخچه آمار"
- **نمایش 30 روز اخیر**: آمار روزانه
- **تحلیل روند**: مقایسه با روز قبل
- **آمار تفصیلی**: نمایش کامل آمار 7 روز اخیر
- **روند درآمد**: تحلیل درآمد در دوره‌های مختلف

### 8. 💾 ذخیره‌سازی پایدار
- **جدول `bot_stats`**: ذخیره آمار روزانه
- **جدول `revenue_stats`**: ذخیره آمار درآمد
- **حفظ اطلاعات**: عدم از دست رفتن داده‌ها با ری‌استارت

## 🔧 تغییرات فنی

### جداول جدید دیتابیس:
```sql
-- کاربران بن شده
banned_users (id, user_id, username, first_name, last_name, banned_by, ban_reason, is_active, created_at, updated_at)

-- آمار درآمد
revenue_stats (id, period_type, period_start, period_end, total_revenue, total_orders, ...)

-- آمار ربات
bot_stats (id, stat_date, total_users, active_users_30d, active_users_7d, ...)
```

### سرویس‌های جدید:
- `services/user_search_service.py`
- `services/server_stats_service.py`
- `services/stats_history_service.py`
- `utils/ban_middleware.py`

### وابستگی جدید:
```
psutil>=5.9.0  # برای آمار سرور
```

## 🚀 نحوه استفاده

### 1. نصب وابستگی جدید:
```bash
pip install psutil>=5.9.0
```

### 2. ری‌استارت ربات:
```bash
python bot.py
```

### 3. تست قابلیت‌ها:
```bash
python test_new_features.py
```

## 🔐 دسترسی‌ها

- **🔍 جست‌وجوی کاربر**: فقط ادمین اصلی
- **🖥️ آمار سرور**: تمام ادمین‌ها
- **📈 تاریخچه آمار**: تمام ادمین‌ها
- **🚫 بن کاربر**: فقط ادمین اصلی

## ⚠️ نکات مهم

1. **بکاپ**: قبل از استفاده حتماً از دیتابیس بکاپ بگیرید
2. **تست**: ابتدا در محیط تست آزمایش کنید
3. **لاگ**: تمام عملیات حساس لاگ می‌شوند
4. **امنیت**: دسترسی‌ها بر اساس سطح ادمین تنظیم شده

## 🐛 عیب‌یابی

اگر مشکلی پیش آمد:
1. فایل `test_new_features.py` را اجرا کنید
2. لاگ‌های ربات را بررسی کنید
3. دیتابیس را بررسی کنید که جداول جدید ایجاد شده‌اند

## 📞 پشتیبانی

در صورت بروز مشکل، لطفاً موارد زیر را ارائه دهید:
- پیام خطا
- لاگ‌های مربوطه
- مراحل انجام شده

---

**نسخه**: 2.0.0  
**تاریخ**: 2025-07-20  
**سازگاری**: Python 3.8+
