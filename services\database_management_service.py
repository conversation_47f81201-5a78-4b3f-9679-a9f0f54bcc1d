from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from typing import Dict, List, Optional
from datetime import datetime

from logger import logger, bot_logger
from error_handler import error_handler
from utils.auth import AuthUtils
from services.excel_management_service import ExcelManagementService
from services.admin_service import AdminService
from database_manager import db_manager

class DatabaseManagementService:
    def __init__(self):
        self.auth_utils = AuthUtils()
        self.excel_service = ExcelManagementService()
        self.admin_service = AdminService()
        logger.info("DatabaseManagementService initialized successfully")
    
    async def show_database_management_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show database management menu"""
        try:
            user_id = update.effective_user.id

            # Check admin permissions
            if not await self.auth_utils.is_admin(user_id):
                await update.message.reply_text("❌ شما دسترسی به این بخش ندارید")
                return

            message = """🗄️ **مدیریت دیتابیس**

🎯 **این بخش برای:**
• مدیریت فایل‌های Excel
• اضافه و حذف کردن اکانت‌ها
• دریافت و بکاپ دیتابیس
• مدیریت کامل اطلاعات

📊 **قابلیت‌ها:**
• نمایش آمار اکانت‌ها
• آپلود و دانلود فایل‌های Excel
• مدیریت اکانت‌های تکی
• بکاپ‌گیری دستی

🔧 **عملیات:**"""

            keyboard = [
                [InlineKeyboardButton("📤 آپلود فایل اکسل", callback_data="db_upload_excel")],
                [InlineKeyboardButton("➕ اضافه کردن اکانت تکی", callback_data="db_add_single")],
                [InlineKeyboardButton("🗑️ حذف کردن اکانت تکی", callback_data="db_delete_single")],
                [InlineKeyboardButton("🗑️ حذف چندتایی", callback_data="db_delete_multiple")],
                [InlineKeyboardButton("📥 دریافت فایل اکسل", callback_data="db_download_excel")],
                [InlineKeyboardButton("📁 بکاپ دستی", callback_data="db_manual_backup")],
                [InlineKeyboardButton("🔄 بروزرسانی", callback_data="refresh_database")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            # Check if this is from callback query or message
            if hasattr(update, 'callback_query') and update.callback_query:
                await update.callback_query.edit_message_text(
                    message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            else:
                await update.message.reply_text(
                    message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

            bot_logger.log_admin_action(user_id, "VIEW_DATABASE_MANAGEMENT", "Viewed database management menu")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_database_management_menu")
    
    async def handle_database_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str) -> None:
        """Handle database management callbacks"""
        try:
            user_id = update.effective_user.id
            
            if not await self.auth_utils.is_admin(user_id):
                await update.callback_query.answer("❌ شما دسترسی به این بخش ندارید", show_alert=True)
                return
            
            if callback_data == "db_upload_excel":
                await self.handle_upload_excel_request(update, context)
            
            elif callback_data == "db_add_single":
                await self.handle_add_single_request(update, context)
            
            elif callback_data == "db_delete_single":
                await self.handle_delete_single_request(update, context)

            elif callback_data == "db_delete_multiple":
                await self.handle_delete_multiple_request(update, context)

            elif callback_data == "db_download_excel":
                await self.handle_download_excel_request(update, context)
            
            elif callback_data == "db_manual_backup":
                await self.handle_manual_backup_request(update, context)

            elif callback_data == "refresh_database":
                await self.refresh_database_menu(update, context)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_database_callback")
    
    async def handle_upload_excel_request(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Excel upload request"""
        try:
            message = """📤 **آپلود فایل اکسل**

لطفاً فایل اکسل (.xlsx) خود را ارسال کنید.

⚠️ **نکات مهم:**
• فایل باید فرمت .xlsx باشد
• ستون‌های مورد نیاز: id, email, pass, q1, q2, q3, date, status
• ترتیب ستون‌ها مهم است
• ID ها به صورت خودکار تولید می‌شوند
• فایل با data.xlsx ادغام می‌شود
• ایمیل‌های تکراری نادیده گرفته می‌شوند

📋 **فرآیند:**
1. فایل شما اعتبارسنجی می‌شود
2. ایمیل‌های تکراری حذف می‌شوند
3. ID های جدید تولید می‌شوند
4. فایل با data.xlsx ادغام می‌شود
5. گزارش کامل ارسال می‌شود

💡 **نکته:** قیمت‌گذاری بر اساس دامنه ایمیل انجام می‌شود

📤 **فایل اکسل خود را ارسال کنید:**"""

            keyboard = [
                [InlineKeyboardButton("🔙 بازگشت", callback_data="database_management")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            context.user_data['waiting_for_excel'] = True
            
            await update.callback_query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_upload_excel_request")
    
    async def handle_add_single_request(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle add single account request"""
        try:
            await self.excel_service.start_single_account_add(update, context)
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_add_single_request")
    
    async def handle_delete_single_request(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle delete single account request"""
        try:
            message = """🗑️ **حذف اکانت تکی**

لطفاً شماره ID اکانتی که می‌خواهید حذف کنید را ارسال کنید.

⚠️ **نکات مهم:**
• فقط عدد ID را ارسال کنید (مثال: 354)
• این عمل غیرقابل بازگشت است
• پس از حذف، ID های بعدی به‌روزرسانی می‌شوند
• برای لغو /cancel را ارسال کنید

🔍 **مثال:** اگر ID 354 را حذف کنید:
• اکانت با ID 354 کاملاً پاک می‌شود
• اکانت با ID 355 به ID 354 تبدیل می‌شود
• و همین‌طور برای بقیه...

📤 **شماره ID را ارسال کنید:**"""

            keyboard = [
                [InlineKeyboardButton("🔙 بازگشت", callback_data="database_management")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            context.user_data['deleting_account'] = True
            
            await update.callback_query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_delete_single_request")
    
    async def handle_download_excel_request(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle download Excel request"""
        try:
            await update.callback_query.answer("📥 در حال آماده‌سازی فایل...", show_alert=False)
            
            # Convert callback query to message-like object for excel_service
            class FakeMessage:
                def __init__(self, chat_id):
                    self.chat = type('obj', (object,), {'id': chat_id})

                async def reply_text(self, text, **kwargs):
                    await update.callback_query.message.reply_text(text, **kwargs)

            class FakeChat:
                def __init__(self, chat_id):
                    self.id = chat_id

            fake_update = type('obj', (object,), {
                'effective_user': update.effective_user,
                'effective_chat': FakeChat(update.effective_user.id),
                'message': FakeMessage(update.effective_user.id)
            })
            
            await self.excel_service.send_excel_file(fake_update, context)
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_download_excel_request")
    
    async def handle_manual_backup_request(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle manual backup request"""
        try:
            await update.callback_query.answer("📁 در حال ایجاد بکاپ...", show_alert=False)
            
            # Convert callback query to message-like object for admin_service
            class FakeMessage:
                def __init__(self, chat_id):
                    self.chat = type('obj', (object,), {'id': chat_id})

                async def reply_text(self, text, **kwargs):
                    await update.callback_query.message.reply_text(text, **kwargs)

            class FakeChat:
                def __init__(self, chat_id):
                    self.id = chat_id

            fake_update = type('obj', (object,), {
                'effective_user': update.effective_user,
                'effective_chat': FakeChat(update.effective_user.id),
                'message': FakeMessage(update.effective_user.id)
            })
            
            await self.admin_service.manual_backup(fake_update, context)
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_manual_backup_request")

    async def refresh_database_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Refresh database management menu"""
        try:
            # Get fresh data for the menu
            user_id = update.effective_user.id

            # Check admin permissions
            if not await self.auth_utils.is_admin(user_id):
                await update.callback_query.answer("❌ شما دسترسی به این بخش ندارید", show_alert=True)
                return

            # Refresh database connections first
            from database_manager import db_manager
            db_manager.initialize_excel_database()
            await db_manager.initialize_sql_database()

            # Get database statistics
            all_apple_ids = db_manager.get_all_apple_ids()

            # Count available and sold accounts
            available_count = 0
            sold_count = 0

            for apple_id in all_apple_ids:
                is_available = not apple_id.get('Status') or str(apple_id.get('Status', '')).strip() == ''
                if is_available:
                    available_count += 1
                else:
                    sold_count += 1

            # Add timestamp to make message unique
            current_time = datetime.now().strftime("%H:%M:%S")

            message = """🗄️ **مدیریت دیتابیس**

🎯 **این بخش برای:**
• مدیریت فایل‌های Excel
• اضافه و حذف کردن اکانت‌ها
• دریافت و بکاپ دیتابیس
• مدیریت کامل اطلاعات

📊 **قابلیت‌ها:**
• نمایش آمار اکانت‌ها
• آپلود و دانلود فایل‌های Excel
• مدیریت اکانت‌های تکی
• بکاپ‌گیری دستی

📈 **آمار فعلی:**
• مجموع اکانت‌ها: {total} عدد
• اکانت‌های موجود: {available} عدد
• اکانت‌های فروخته شده: {sold} عدد

🔄 **آخرین بروزرسانی:** {time}

🔧 **عملیات:**""".format(
                total=len(all_apple_ids),
                available=available_count,
                sold=sold_count,
                time=current_time
            )

            keyboard = [
                [InlineKeyboardButton("📤 آپلود فایل اکسل", callback_data="db_upload_excel")],
                [InlineKeyboardButton("➕ اضافه کردن اکانت تکی", callback_data="db_add_single")],
                [InlineKeyboardButton("🗑️ حذف کردن اکانت تکی", callback_data="db_delete_single")],
                [InlineKeyboardButton("📥 دریافت فایل اکسل", callback_data="db_download_excel")],
                [InlineKeyboardButton("📁 بکاپ دستی", callback_data="db_manual_backup")],
                [InlineKeyboardButton("🔄 بروزرسانی", callback_data="refresh_database")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            try:
                await update.callback_query.edit_message_text(
                    message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                await update.callback_query.answer("✅ اطلاعات به‌روزرسانی شد", show_alert=False)
            except Exception as edit_error:
                # Handle "Message is not modified" error gracefully
                if "Message is not modified" in str(edit_error):
                    await update.callback_query.answer("✅ اطلاعات به‌روزرسانی شد", show_alert=False)
                else:
                    raise edit_error

            bot_logger.log_admin_action(user_id, "REFRESH_DATABASE_MANAGEMENT", "Database management menu refreshed")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "refresh_database_menu")

    async def handle_delete_multiple_request(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle multiple delete request"""
        try:
            user_id = update.effective_user.id

            if not await self.auth_utils.is_admin(user_id):
                await update.callback_query.answer("❌ شما دسترسی به این بخش ندارید", show_alert=True)
                return

            context.user_data['admin_deleting_multiple'] = True

            message = """🗑️ **حذف چندتایی اکانت‌ها**

لطفاً شماره ردیف‌های مورد نظر برای حذف را به یکی از روش‌های زیر ارسال کنید:

**روش اول - فهرست:**
```
256,248,232,189
```

**روش دوم - بازه:**
```
256 تا 300
```

**روش سوم - ترکیبی:**
```
100,150 تا 200,250,300 تا 350
```

⚠️ **توجه:** این عملیات غیرقابل بازگشت است!

💡 **نکته:** شماره ردیف‌ها را از فایل اکسل یا لیست اکانت‌ها مشاهده کنید."""

            keyboard = [[InlineKeyboardButton("❌ انصراف", callback_data="database_management")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                message, parse_mode='Markdown', reply_markup=reply_markup
            )

            bot_logger.log_admin_action(user_id, "REQUEST_DELETE_MULTIPLE", "Requested multiple delete")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "handle_delete_multiple_request")

    async def process_multiple_delete(self, update: Update, context: ContextTypes.DEFAULT_TYPE, delete_text: str) -> None:
        """Process multiple delete request"""
        try:
            user_id = update.effective_user.id

            if not await self.auth_utils.is_admin(user_id):
                await update.message.reply_text("❌ شما دسترسی به این بخش ندارید")
                return

            # Parse the delete text to get row numbers
            row_numbers = self.parse_delete_text(delete_text)

            if not row_numbers:
                await update.message.reply_text("❌ فرمت ورودی نامعتبر است. لطفاً دوباره تلاش کنید.")
                return

            # Sort row numbers in descending order to avoid index shifting during deletion
            row_numbers = sorted(set(row_numbers), reverse=True)

            # Get current data
            data = db_manager.get_all_apple_ids()

            if not data:
                await update.message.reply_text("❌ هیچ اکانتی در دیتابیس موجود نیست.")
                return

            # Validate row numbers
            invalid_rows = [row for row in row_numbers if row < 1 or row > len(data)]
            if invalid_rows:
                await update.message.reply_text(
                    f"❌ شماره ردیف‌های نامعتبر: {', '.join(map(str, invalid_rows))}\n"
                    f"شماره ردیف‌ها باید بین 1 تا {len(data)} باشند."
                )
                return

            # Delete accounts from Excel files
            deleted_count = 0
            deleted_accounts = []

            # Collect accounts to delete
            for row_num in row_numbers:
                # Convert to 0-based index
                index = row_num - 1
                if 0 <= index < len(data):
                    account = data[index]
                    deleted_accounts.append(f"ردیف {row_num}: {account.get('Email', 'N/A')}")

            # Delete from Excel files
            success = await self.delete_accounts_from_excel(row_numbers)

            if success:
                deleted_count = len(row_numbers)

                # Get remaining count
                remaining_data = db_manager.get_all_apple_ids()
                remaining_count = len(remaining_data)

                message = f"✅ **حذف چندتایی موفق**\n\n"
                message += f"🗑️ **تعداد حذف شده:** {deleted_count} اکانت\n"
                message += f"📊 **تعداد باقی‌مانده:** {remaining_count} اکانت\n\n"
                message += "**اکانت‌های حذف شده:**\n"

                # Show first 10 deleted accounts
                for i, account in enumerate(deleted_accounts[:10]):
                    message += f"• {account}\n"

                if len(deleted_accounts) > 10:
                    message += f"• ... و {len(deleted_accounts) - 10} اکانت دیگر\n"

                message += "\n🔄 **شماره ردیف‌ها مجدداً سازماندهی شدند.**"

                await update.message.reply_text(message, parse_mode='Markdown')

                bot_logger.log_admin_action(
                    user_id,
                    "DELETE_MULTIPLE_SUCCESS",
                    f"Deleted {deleted_count} accounts: {', '.join(map(str, row_numbers))}"
                )
            else:
                await update.message.reply_text("❌ خطا در حذف اکانت‌ها. لطفاً دوباره تلاش کنید.")

            # Clear user state
            context.user_data.pop('admin_deleting_multiple', None)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "process_multiple_delete")

    def parse_delete_text(self, text: str) -> List[int]:
        """Parse delete text to extract row numbers"""
        try:
            row_numbers = []

            # Split by comma first
            parts = [part.strip() for part in text.split(',')]

            for part in parts:
                if 'تا' in part:
                    # Handle range: "256 تا 300"
                    range_parts = [p.strip() for p in part.split('تا')]
                    if len(range_parts) == 2:
                        try:
                            start = int(range_parts[0])
                            end = int(range_parts[1])
                            if start <= end:
                                row_numbers.extend(range(start, end + 1))
                        except ValueError:
                            continue
                else:
                    # Handle single number
                    try:
                        row_numbers.append(int(part))
                    except ValueError:
                        continue

            return row_numbers

        except Exception:
            return []

    async def delete_accounts_from_excel(self, row_numbers: List[int]) -> bool:
        """Delete accounts from Excel files by row numbers"""
        try:
            import openpyxl
            import glob
            import os

            # Get all Excel files
            excel_pattern = os.path.join("database", "*.xlsx")
            excel_files = glob.glob(excel_pattern)

            if not excel_files:
                logger.error("No Excel files found")
                return False

            # Sort row numbers in descending order to avoid index shifting
            row_numbers = sorted(set(row_numbers), reverse=True)

            for file_path in excel_files:
                try:
                    workbook = openpyxl.load_workbook(file_path)
                    worksheet = workbook.active

                    # Delete rows (add 1 because Excel is 1-based and we have header)
                    for row_num in row_numbers:
                        excel_row = row_num + 1  # +1 for header row
                        if excel_row <= worksheet.max_row:
                            worksheet.delete_rows(excel_row)

                    # Reorganize IDs
                    for row_idx, row in enumerate(worksheet.iter_rows(min_row=2), start=1):
                        if row[0].value is not None:  # If row has data
                            row[0].value = row_idx  # Set new ID

                    workbook.save(file_path)
                    workbook.close()

                    logger.info(f"Deleted {len(row_numbers)} accounts from {file_path}")

                except Exception as e:
                    logger.error(f"Error deleting from {file_path}: {str(e)}")
                    continue

            return True

        except Exception as e:
            logger.error(f"Error in delete_accounts_from_excel: {str(e)}")
            return False

# Global instance
database_management_service = DatabaseManagementService()
