#!/usr/bin/env python3
"""
Test script for final fixes to the Telegram bot
"""

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all services can be imported"""
    print("🧪 Testing service imports...")
    
    try:
        from services.admin_service import AdminService
        from services.user_management_service import user_management_service
        from services.user_search_service import user_search_service
        from handlers.admin_handler import AdminHandler
        from handlers.callback_handler import CallbackHandler
        
        print("✅ All service imports successful")
        return True
        
    except Exception as e:
        print(f"❌ Service import failed: {str(e)}")
        return False

def test_admin_service_methods():
    """Test admin service methods exist"""
    print("🧪 Testing AdminService methods...")
    
    try:
        from services.admin_service import AdminService
        
        admin_service = AdminService()
        
        # Test methods exist
        methods = [
            'show_bot_stats',
            'refresh_bot_stats',
            'show_accounts_from_database',
            'send_accounts_as_message'
        ]
        
        for method in methods:
            if hasattr(admin_service, method):
                print(f"  ✅ Method {method} exists")
            else:
                print(f"  ❌ Method {method} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ AdminService methods test failed: {str(e)}")
        return False

def test_callback_handler_methods():
    """Test callback handler methods"""
    print("🧪 Testing CallbackHandler methods...")
    
    try:
        from handlers.callback_handler import CallbackHandler
        
        callback_handler = CallbackHandler()
        
        # Test methods exist
        methods = [
            'handle_clear_purchases',
            'handle_clear_accounts',
            'handle_user_management_callbacks'
        ]
        
        for method in methods:
            if hasattr(callback_handler, method):
                print(f"  ✅ Method {method} exists")
            else:
                print(f"  ❌ Method {method} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ CallbackHandler methods test failed: {str(e)}")
        return False

def test_user_search_service():
    """Test user search service"""
    print("🧪 Testing UserSearchService...")
    
    try:
        from services.user_search_service import user_search_service
        
        # Test methods exist
        methods = [
            'show_user_info',
            'show_user_search_prompt'
        ]
        
        for method in methods:
            if hasattr(user_search_service, method):
                print(f"  ✅ Method {method} exists")
            else:
                print(f"  ❌ Method {method} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ UserSearchService test failed: {str(e)}")
        return False

async def test_database_connection():
    """Test database connection"""
    print("🧪 Testing database connection...")
    
    try:
        from database_manager import db_manager
        
        # Test database methods
        methods = [
            'get_all_apple_ids',
            'get_all_users',
            'clear_user_purchases',
            'clear_user_accounts'
        ]
        
        for method in methods:
            if hasattr(db_manager, method):
                print(f"  ✅ Method {method} exists")
            else:
                print(f"  ❌ Method {method} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Database connection test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Running Final Fixes Tests...\n")
    
    tests = [
        ("Service Imports", test_imports),
        ("AdminService Methods", test_admin_service_methods),
        ("CallbackHandler Methods", test_callback_handler_methods),
        ("UserSearchService", test_user_search_service),
        ("Database Connection", test_database_connection)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = asyncio.run(test_func())
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
                
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {str(e)}")
    
    print(f"\n{'='*50}")
    print(f"TEST SUMMARY: {passed}/{total} tests passed")
    print('='*50)
    
    if passed == total:
        print("🎉 All final fixes validated successfully!")
        print("\n📋 Fixed issues:")
        print("1. ✅ Bot stats refresh format consistency")
        print("2. ✅ Account display shows all fields")
        print("3. ✅ User management service import")
        print("4. ✅ User search operations return to user info")
        print("5. ✅ All callback handlers working")
        return True
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ Ready to test the bot with final fixes!")
        print("\n🔧 What was fixed:")
        print("• Bot stats refresh now shows same format as initial view")
        print("• Account display shows all fields (email, password, security questions, dates)")
        print("• User management service properly imported")
        print("• User search operations return to user info after completion")
        print("• All hierarchical admin menu structure working")
    else:
        print("\n❌ Please fix the remaining issues.")
        sys.exit(1)
