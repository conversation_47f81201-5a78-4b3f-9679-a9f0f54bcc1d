import os
import warnings
import asyncio
import signal
import hashlib
import requests
import threading

from dotenv import load_dotenv
from telegram import Update
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters

# Filter out PTBUserWarning about ApplicationBuilder
warnings.filterwarnings("ignore", message=".*Application.*instances should be built via.*ApplicationBuilder.*")

# Import custom modules
from logger import logger
from handlers.start_handler import StartHandler
from handlers.message_handler import MessageHandler as CustomMessageHandler
from handlers.callback_handler import callback_handler
from error_handler import global_error_handler
from database_manager import db_manager
from models.purchase_plans import purchase_plans_model
from services.backup_service import initialize_backup_service

# License checker implementation
class LicenseChecker:
    def __init__(self):
        self.license_key = os.getenv('LICENSE_KEY')
        self.api_url = os.getenv('LICENSE_API_URL', 'http://38.180.138.154:8080')
        self.api_secret = os.getenv('API_SECRET')
        self.check_interval = 86400  # 24 hours (1 day) - hardcoded for security
        self.shutdown_event = asyncio.Event()
        self.api_key = None

        if self.api_secret:
            self.api_key = hashlib.sha256(self.api_secret.encode()).hexdigest()[:32]

        # Log the check interval
        hours = self.check_interval / 3600
        logger.info(f"License check interval set to {self.check_interval} seconds ({hours} hours)")

    def validate_license(self):
        """Validate license with API server"""
        if not all([self.license_key, self.api_key]):
            return False

        try:
            headers = {
                'Content-Type': 'application/json',
                'X-API-Key': self.api_key
            }

            response = requests.post(
                f"{self.api_url}/validate",
                json={"license_key": self.license_key},
                headers=headers,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('valid'):
                    return True
                else:
                    return False
            else:
                return False

        except Exception as e:
            return False

    def disconnect_license(self):
        """Record disconnection with API"""
        if not all([self.license_key, self.api_key]):
            return

        try:
            headers = {
                'Content-Type': 'application/json',
                'X-API-Key': self.api_key
            }

            requests.post(
                f"{self.api_url}/disconnect",
                json={"license_key": self.license_key},
                headers=headers,
                timeout=5
            )
            pass
        except:
            pass  # Ignore errors during disconnection

    async def periodic_check(self):
        """Periodically check license validity - runs every 24 hours (hardcoded for security)"""
        logger.info(f"Starting license monitoring - will check every {self.check_interval} seconds (24 hours)")
        while not self.shutdown_event.is_set():
            try:
                # Wait for check interval or shutdown event
                await asyncio.wait_for(
                    self.shutdown_event.wait(),
                    timeout=self.check_interval
                )
                break  # Shutdown event was set
            except asyncio.TimeoutError:
                # Time to check license
                if not self.validate_license():
                    self.shutdown_bot()
                    break

    def shutdown_bot(self):
        """Shutdown the bot"""
        # Record disconnection
        self.disconnect_license()

        # Set shutdown event
        self.shutdown_event.set()

        # Force exit
        os._exit(1)

    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.disconnect_license()
        self.shutdown_event.set()

# Global license checker instance
license_checker = LicenseChecker()

def check_license():
    """Check license validity"""
    return license_checker.validate_license()

def start_license_monitoring():
    """Start license monitoring in background"""
    def run_monitoring():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(license_checker.periodic_check())

    monitoring_thread = threading.Thread(target=run_monitoring, daemon=True)
    monitoring_thread.start()

    # Set up signal handlers
    signal.signal(signal.SIGINT, license_checker.signal_handler)
    signal.signal(signal.SIGTERM, license_checker.signal_handler)

def shutdown_license_checker():
    """Shutdown license checker"""
    license_checker.disconnect_license()
    license_checker.shutdown_event.set()

# Load environment variables
load_dotenv()

class AppleIDBot:
    def __init__(self):
        self.bot_token = os.getenv('BOT_TOKEN')

        if not self.bot_token:
            logger.critical("BOT_TOKEN not found in environment variables")
            raise ValueError("BOT_TOKEN is required")

        # Initialize handlers
        self.start_handler = StartHandler()
        self.message_handler = CustomMessageHandler()

        logger.info("AppleIDBot initialized successfully")

    async def initialize_database(self):
        """Initialize database and create default data"""
        try:
            logger.info("Initializing databases...")

            # Initialize SQL database
            await db_manager.initialize_sql_database()

            # Default purchase plans creation is disabled
            # Admin should create domain-based plans manually through the admin panel
            logger.info("Default purchase plans creation is disabled - admin should create plans manually")

            logger.info("Database initialization completed")

        except Exception as e:
            logger.critical(f"Failed to initialize database: {str(e)}")
            raise

    async def run(self):
        """Run the bot"""
        try:
            logger.info("Starting Apple ID Bot...")

            # Check license first - MANDATORY
            logger.info("Checking license...")
            if not check_license():
                logger.critical("❌ License validation failed - cannot start bot")
                logger.critical("❌ Contact @mbnsubmanager_bot for a valid license")
                raise SystemExit("Invalid or expired license")
            logger.info("✅ License validated successfully")

            # Start license monitoring in background
            start_license_monitoring()
            logger.info("🔒 License monitoring started")

            # Initialize database first
            await self.initialize_database()

            # Create application
            application = Application.builder().token(self.bot_token).build()

            # Initialize backup service
            backup_service = initialize_backup_service(application)
            backup_service.start_scheduler()

            # Add handlers
            application.add_handler(CommandHandler("start", self.start_handler.handle_start))
            application.add_handler(CommandHandler("plans", self.start_handler.handle_plans_command))
            application.add_handler(CommandHandler("wallet", self.start_handler.handle_wallet_command))
            application.add_handler(CommandHandler("support", self.start_handler.handle_support_command))
            application.add_handler(CommandHandler("help", self.start_handler.handle_help_command))
            application.add_handler(CommandHandler("cancel", self.start_handler.handle_cancel_command))
            application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.message_handler.handle_message))
            application.add_handler(MessageHandler(filters.Document.ALL, self.message_handler.handle_document))
            application.add_handler(MessageHandler(filters.PHOTO, self.message_handler.handle_photo))
            application.add_handler(MessageHandler(filters.VIDEO, self.message_handler.handle_video))
            application.add_handler(MessageHandler(filters.AUDIO, self.message_handler.handle_audio))
            application.add_handler(CallbackQueryHandler(callback_handler.handle_callback_query))

            # Add error handler
            application.add_error_handler(global_error_handler)

            logger.info("Bot handlers registered successfully")

            # Run the bot
            logger.info("Bot is starting to poll...")
            await application.initialize()
            await application.start()
            await application.updater.start_polling(allowed_updates=Update.ALL_TYPES)

            # Keep the bot running with graceful shutdown
            import asyncio
            import signal

            # Create shutdown event
            shutdown_event = asyncio.Event()

            def signal_handler(signum, frame):
                logger.info(f"Received signal {signum}, initiating graceful shutdown...")
                shutdown_event.set()

            # Register signal handlers
            signal.signal(signal.SIGTERM, signal_handler)
            signal.signal(signal.SIGINT, signal_handler)

            try:
                # Wait for shutdown signal
                await shutdown_event.wait()
            except KeyboardInterrupt:
                logger.info("Received KeyboardInterrupt, shutting down...")

            # Graceful shutdown
            logger.info("Stopping bot...")
            await application.updater.stop()
            await application.stop()
            await application.shutdown()
            logger.info("Bot stopped gracefully")

        except Exception as e:
            logger.critical(f"Critical error starting bot: {str(e)}")
            # Shutdown license checker
            shutdown_license_checker()

            # Try to cleanup application if it exists
            try:
                if 'application' in locals():
                    await application.updater.stop()
                    await application.stop()
                    await application.shutdown()
            except Exception as cleanup_error:
                logger.error(f"Error during cleanup: {str(cleanup_error)}")

            raise

if __name__ == "__main__":
    import asyncio

    async def main():
        bot = None
        try:
            logger.info("Starting Apple ID Bot...")
            bot = AppleIDBot()
            await bot.run()
        except KeyboardInterrupt:
            logger.info("Bot stopped by user (KeyboardInterrupt)")
        except SystemExit:
            logger.info("Bot stopped by system exit")
        except Exception as e:
            logger.critical(f"Bot crashed: {str(e)}")
            import sys
            sys.exit(1)
        finally:
            # Shutdown license checker
            shutdown_license_checker()
            logger.info("License checker shutdown")
            db_manager.close_all_connections()
            logger.info("Bot shutdown complete")

    # Run the bot with proper error handling
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bot interrupted during startup")
        import sys
        sys.exit(0)
    except Exception as e:
        logger.critical(f"Failed to start bot: {str(e)}")
        import sys
        sys.exit(1)
