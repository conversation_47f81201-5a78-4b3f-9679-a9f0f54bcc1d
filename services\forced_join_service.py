"""
Forced Join Channels Management Service
Handles forced join channels functionality for the Apple ID Bot
"""

import asyncio
from typing import List, Dict, Any, Optional
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, Bot
from telegram.ext import ContextTypes
from telegram.error import TelegramError, BadRequest, Forbidden

from logger import logger
from database_manager import db_manager
from utils.auth import AuthUtils
from error_handler import error_handler
from logger import bot_logger


class ForcedJoinService:
    def __init__(self):
        self.auth_utils = AuthUtils()
        logger.info("ForcedJoinService initialized successfully")

    async def show_forced_join_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show forced join channels management menu"""
        try:
            user_id = update.effective_user.id

            # Check admin permissions
            if not await self.auth_utils.is_admin(user_id):
                await update.message.reply_text("❌ شما دسترسی به این بخش ندارید")
                return

            # Get all channels
            channels = await db_manager.get_forced_join_channels()
            active_channels = [ch for ch in channels if ch['is_active']]
            inactive_channels = [ch for ch in channels if not ch['is_active']]

            message = f"""📢 **مدیریت کانال‌های جوین اجباری**

📊 **آمار کانال‌ها:**
• کانال‌های فعال: {len(active_channels)}
• کانال‌های غیرفعال: {len(inactive_channels)}
• مجموع: {len(channels)}

⚙️ **عملیات موجود:**
• اضافه کردن کانال جدید
• مشاهده لیست کانال‌ها
• حذف کانال
• فعال/غیرفعال کردن کانال

💡 **راهنما:**
• کاربران باید عضو تمام کانال‌های فعال باشند
• در صورت عدم عضویت، دسترسی به ربات محدود می‌شود
• آیدی کانال باید با @ شروع شود یا عددی باشد (مثل -100123456789)
"""

            keyboard = [
                [
                    InlineKeyboardButton("➕ اضافه کردن کانال", callback_data="add_forced_channel"),
                    InlineKeyboardButton("📋 لیست کانال‌ها", callback_data="list_forced_channels")
                ],
                [
                    InlineKeyboardButton("🔄 بروزرسانی", callback_data="refresh_forced_channels")
                ]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            # Add timestamp for refresh to ensure message is different
            if update.callback_query and update.callback_query.data == "refresh_forced_channels":
                from datetime import datetime
                timestamp = datetime.now().strftime("%H:%M:%S")
                message += f"\n\n🕐 آخرین بروزرسانی: {timestamp}"

            if update.callback_query:
                try:
                    await update.callback_query.edit_message_text(
                        message,
                        parse_mode='Markdown',
                        reply_markup=reply_markup
                    )
                except Exception as edit_error:
                    # Handle "Message is not modified" error gracefully
                    if "Message is not modified" in str(edit_error):
                        await update.callback_query.answer("✅ اطلاعات به‌روزرسانی شد", show_alert=False)
                    else:
                        raise edit_error
            else:
                await update.message.reply_text(
                    message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_forced_join_management")

    async def start_add_channel_process(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Start the process of adding a new forced join channel"""
        try:
            user_id = update.effective_user.id

            # Check admin permissions
            if not await self.auth_utils.is_admin(user_id):
                await update.callback_query.answer("❌ شما دسترسی به این بخش ندارید", show_alert=True)
                return

            message = """➕ **اضافه کردن کانال جوین اجباری**

📝 **راهنما:**
• آیدی کانال را ارسال کنید
• آیدی می‌تواند یوزرنیم کانال باشد (مثل @mychannel)
• یا آیدی عددی کانال (مثل -100123456789)
• ربات باید ادمین کانال باشد تا بتواند عضویت را چک کند

💡 **نکته:** برای لغو عملیات، دستور /cancel را ارسال کنید"""

            keyboard = [[InlineKeyboardButton("❌ لغو", callback_data="forced_join_management")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            # Set user state
            context.user_data['adding_forced_channel'] = True

        except Exception as e:
            await error_handler.handle_error(update, context, e, "start_add_channel_process")

    async def process_add_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE, channel_input: str) -> None:
        """Process adding a new forced join channel"""
        try:
            user_id = update.effective_user.id

            # Check admin permissions
            if not await self.auth_utils.is_admin(user_id):
                await update.message.reply_text("❌ شما دسترسی به این بخش ندارید")
                return

            # Clean up user state
            context.user_data.pop('adding_forced_channel', None)

            # Validate channel input
            channel_input = channel_input.strip()
            if not channel_input:
                await update.message.reply_text("❌ آیدی کانال نمی‌تواند خالی باشد")
                return

            # Check if channel already exists
            existing_channels = await db_manager.get_forced_join_channels(active_only=False)
            for channel in existing_channels:
                if channel['channel_id'] == channel_input:
                    await update.message.reply_text(f"❌ این کانال قبلاً اضافه شده است\n\n🆔 **کانال:** `{channel_input}`")
                    return

            # Try to get channel info from Telegram
            channel_info = await self.get_channel_info(context.bot, channel_input)
            
            if not channel_info:
                await update.message.reply_text(
                    f"⚠️ **هشدار:** نمی‌توان اطلاعات کانال را دریافت کرد\n\n"
                    f"🆔 **کانال:** `{channel_input}`\n\n"
                    f"**دلایل احتمالی:**\n"
                    f"• ربات ادمین کانال نیست\n"
                    f"• آیدی کانال اشتباه است\n"
                    f"• کانال خصوصی است\n\n"
                    f"**آیا می‌خواهید به هر حال کانال را اضافه کنید؟**",
                    parse_mode='Markdown'
                )
                
                keyboard = [
                    [
                        InlineKeyboardButton("✅ بله، اضافه کن", callback_data=f"force_add_channel_{channel_input}"),
                        InlineKeyboardButton("❌ خیر، لغو کن", callback_data="forced_join_management")
                    ]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await update.message.reply_text(
                    "انتخاب کنید:",
                    reply_markup=reply_markup
                )
                return

            # Add channel to database
            success = await db_manager.add_forced_join_channel(
                channel_id=channel_input,
                channel_title=channel_info.get('title'),
                channel_username=channel_info.get('username'),
                added_by=user_id
            )

            if success:
                message = f"""✅ **کانال با موفقیت اضافه شد**

🆔 **آیدی کانال:** `{channel_input}`
📝 **نام کانال:** {channel_info.get('title', 'نامشخص')}
👤 **یوزرنیم:** @{channel_info.get('username', 'ندارد')}
👥 **تعداد اعضا:** {channel_info.get('member_count', 'نامشخص')}
⏰ **زمان اضافه شدن:** {channel_info.get('created_at', 'الان')}

💡 **نکته:** کانال به صورت فعال اضافه شده و کاربران باید عضو آن باشند"""

                keyboard = [[InlineKeyboardButton("🔙 بازگشت به مدیریت کانال‌ها", callback_data="forced_join_management")]]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

                bot_logger.log_admin_action(
                    user_id,
                    "ADD_FORCED_CHANNEL",
                    f"Added forced join channel: {channel_input}"
                )
            else:
                await update.message.reply_text("❌ خطا در اضافه کردن کانال. لطفاً دوباره تلاش کنید.")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "process_add_channel")

    async def force_add_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE, channel_id: str) -> None:
        """Force add channel without validation"""
        try:
            user_id = update.effective_user.id

            success = await db_manager.add_forced_join_channel(
                channel_id=channel_id,
                channel_title="نامشخص",
                channel_username=None,
                added_by=user_id
            )

            if success:
                message = f"""✅ **کانال اجباری اضافه شد**

🆔 **آیدی کانال:** `{channel_id}`
⚠️ **توجه:** اطلاعات کانال قابل دریافت نبود

💡 **نکته:** لطفاً مطمئن شوید که:
• آیدی کانال صحیح است
• ربات ادمین کانال است
• کانال عمومی یا ربات دسترسی دارد"""

                keyboard = [[InlineKeyboardButton("🔙 بازگشت", callback_data="forced_join_management")]]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.callback_query.edit_message_text(
                    message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

                bot_logger.log_admin_action(
                    user_id,
                    "FORCE_ADD_CHANNEL",
                    f"Force added channel: {channel_id}"
                )
            else:
                await update.callback_query.answer("❌ خطا در اضافه کردن کانال", show_alert=True)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "force_add_channel")

    async def get_channel_info(self, bot: Bot, channel_id: str) -> Optional[Dict[str, Any]]:
        """Get channel information from Telegram"""
        try:
            chat = await bot.get_chat(channel_id)
            
            return {
                'id': chat.id,
                'title': chat.title,
                'username': chat.username,
                'type': chat.type,
                'member_count': getattr(chat, 'member_count', None)
            }
            
        except (TelegramError, BadRequest, Forbidden) as e:
            logger.warning(f"Could not get channel info for {channel_id}: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Error getting channel info for {channel_id}: {str(e)}")
            return None

    async def show_channels_list(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show list of forced join channels"""
        try:
            channels = await db_manager.get_forced_join_channels(active_only=False)

            if not channels:
                message = """📋 **لیست کانال‌های جوین اجباری**

❌ **هیچ کانالی تعریف نشده است**

💡 **برای اضافه کردن کانال جدید از دکمه زیر استفاده کنید**"""

                keyboard = [
                    [InlineKeyboardButton("➕ اضافه کردن کانال", callback_data="add_forced_channel")],
                    [InlineKeyboardButton("🔙 بازگشت", callback_data="forced_join_management")]
                ]
            else:
                message = f"""📋 **لیست کانال‌های جوین اجباری**

📊 **تعداد کل:** {len(channels)}

"""

                for i, channel in enumerate(channels, 1):
                    status = "🟢 فعال" if channel['is_active'] else "🔴 غیرفعال"
                    title = channel['channel_title'] or "نامشخص"
                    username = f"@{channel['channel_username']}" if channel['channel_username'] else "ندارد"

                    message += f"""**{i}.** {title}
🆔 `{channel['channel_id']}`
👤 {username}
📊 {status}

"""

                keyboard = []
                # Add toggle buttons for each channel (max 5 per row)
                for i in range(0, len(channels), 2):
                    row = []
                    for j in range(i, min(i + 2, len(channels))):
                        channel = channels[j]
                        action = "deactivate" if channel['is_active'] else "activate"
                        emoji = "🔴" if channel['is_active'] else "🟢"
                        row.append(InlineKeyboardButton(
                            f"{emoji} {j+1}",
                            callback_data=f"toggle_channel_{action}_{channel['channel_id']}"
                        ))
                    keyboard.append(row)

                # Add delete buttons
                delete_row = []
                for i, channel in enumerate(channels):
                    if i < 5:  # Limit to first 5 channels to avoid button limit
                        delete_row.append(InlineKeyboardButton(
                            f"🗑️ {i+1}",
                            callback_data=f"delete_channel_{channel['channel_id']}"
                        ))
                if delete_row:
                    keyboard.append(delete_row)

                keyboard.extend([
                    [InlineKeyboardButton("➕ اضافه کردن کانال", callback_data="add_forced_channel")],
                    [InlineKeyboardButton("🔙 بازگشت", callback_data="forced_join_management")]
                ])

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_channels_list")

    async def toggle_channel_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE,
                                  action: str, channel_id: str) -> None:
        """Toggle channel active status"""
        try:
            user_id = update.effective_user.id

            # Check admin permissions
            if not await self.auth_utils.is_admin(user_id):
                await update.callback_query.answer("❌ شما دسترسی به این بخش ندارید", show_alert=True)
                return

            is_active = action == "activate"
            success = await db_manager.toggle_forced_join_channel(channel_id, is_active)

            if success:
                status_text = "فعال" if is_active else "غیرفعال"
                await update.callback_query.answer(f"✅ کانال {status_text} شد")

                bot_logger.log_admin_action(
                    user_id,
                    "TOGGLE_FORCED_CHANNEL",
                    f"Channel {channel_id} {status_text}"
                )

                # Refresh the list
                await self.show_channels_list(update, context)
            else:
                await update.callback_query.answer("❌ خطا در تغییر وضعیت کانال", show_alert=True)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "toggle_channel_status")

    async def delete_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE, channel_id: str) -> None:
        """Delete a forced join channel"""
        try:
            user_id = update.effective_user.id

            # Check admin permissions
            if not await self.auth_utils.is_admin(user_id):
                await update.callback_query.answer("❌ شما دسترسی به این بخش ندارید", show_alert=True)
                return

            # Get channel info for confirmation
            channels = await db_manager.get_forced_join_channels(active_only=False)
            channel_info = None
            for ch in channels:
                if ch['channel_id'] == channel_id:
                    channel_info = ch
                    break

            if not channel_info:
                await update.callback_query.answer("❌ کانال یافت نشد", show_alert=True)
                return

            message = f"""🗑️ **تایید حذف کانال**

🆔 **آیدی کانال:** `{channel_id}`
📝 **نام کانال:** {channel_info['channel_title'] or 'نامشخص'}
👤 **یوزرنیم:** @{channel_info['channel_username'] or 'ندارد'}

⚠️ **هشدار:** این عمل قابل بازگشت نیست!

**آیا مطمئن هستید؟**"""

            keyboard = [
                [
                    InlineKeyboardButton("✅ بله، حذف کن", callback_data=f"confirm_delete_channel_{channel_id}"),
                    InlineKeyboardButton("❌ خیر، لغو کن", callback_data="list_forced_channels")
                ]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "delete_channel")

    async def confirm_delete_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE, channel_id: str) -> None:
        """Confirm and delete a forced join channel"""
        try:
            user_id = update.effective_user.id

            # Check admin permissions
            if not await self.auth_utils.is_admin(user_id):
                await update.callback_query.answer("❌ شما دسترسی به این بخش ندارید", show_alert=True)
                return

            success = await db_manager.remove_forced_join_channel(channel_id)

            if success:
                await update.callback_query.answer("✅ کانال با موفقیت حذف شد")

                bot_logger.log_admin_action(
                    user_id,
                    "DELETE_FORCED_CHANNEL",
                    f"Deleted channel: {channel_id}"
                )

                # Go back to channels list
                await self.show_channels_list(update, context)
            else:
                await update.callback_query.answer("❌ خطا در حذف کانال", show_alert=True)

        except Exception as e:
            await error_handler.handle_error(update, context, e, "confirm_delete_channel")

    async def check_user_membership(self, bot: Bot, user_id: int) -> Dict[str, Any]:
        """Check if user is member of all forced join channels"""
        try:
            channels = await db_manager.get_forced_join_channels(active_only=True)

            if not channels:
                return {
                    'is_member_of_all': True,
                    'missing_channels': [],
                    'total_channels': 0
                }

            missing_channels = []

            for channel in channels:
                try:
                    member = await bot.get_chat_member(channel['channel_id'], user_id)

                    logger.debug(f"User {user_id} status in channel {channel['channel_id']}: {member.status}")

                    # Check if user is actually a member
                    # Valid member statuses: member, administrator, creator
                    # Invalid statuses: left, kicked, restricted
                    if member.status not in ['member', 'administrator', 'creator']:
                        logger.info(f"User {user_id} is not a member of channel {channel['channel_id']} (status: {member.status})")
                        missing_channels.append(channel)
                    else:
                        logger.debug(f"User {user_id} is a member of channel {channel['channel_id']} (status: {member.status})")

                except (TelegramError, BadRequest, Forbidden) as e:
                    # If we can't check membership, assume user is not a member
                    logger.warning(f"Cannot check membership for user {user_id} in channel {channel['channel_id']}: {str(e)}")
                    missing_channels.append(channel)

                except Exception as e:
                    logger.error(f"Error checking membership for user {user_id} in channel {channel['channel_id']}: {str(e)}")
                    missing_channels.append(channel)

            return {
                'is_member_of_all': len(missing_channels) == 0,
                'missing_channels': missing_channels,
                'total_channels': len(channels)
            }

        except Exception as e:
            logger.error(f"Error checking user membership for user {user_id}: {str(e)}")
            return {
                'is_member_of_all': False,
                'missing_channels': [],
                'total_channels': 0
            }

# Create global instance
forced_join_service = ForcedJoinService()
