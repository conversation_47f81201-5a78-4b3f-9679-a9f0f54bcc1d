import aiosqlite
import json
from typing import List, Dict, Optional
from datetime import datetime

from logger import logger, bot_logger
from database_manager import db_manager

class UserPurchasesModel:
    def __init__(self):
        self.db_path = db_manager.sql_db_path
        logger.info("UserPurchasesModel initialized successfully")
    
    async def get_user_purchases(self, user_id: int) -> List[Dict]:
        """Get all purchases for a specific user"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute('''
                    SELECT p.id, p.apple_ids, p.total_price, p.purchase_date, p.warranty_until,
                           pp.name as plan_name, pp.quantity, pp.has_warranty, pp.warranty_days
                    FROM purchases p
                    LEFT JOIN purchase_plans pp ON p.plan_id = pp.id
                    WHERE p.user_id = ?
                    ORDER BY p.purchase_date DESC
                ''', (user_id,))
                
                purchases = await cursor.fetchall()
                
                result = []
                for purchase in purchases:
                    purchase_id, apple_ids_json, total_price, purchase_date, warranty_until, plan_name, quantity, has_warranty, warranty_days = purchase
                    
                    # Parse Apple IDs from JSON
                    try:
                        apple_ids = json.loads(apple_ids_json) if apple_ids_json else []
                    except:
                        apple_ids = []
                    
                    result.append({
                        'id': purchase_id,
                        'apple_ids': apple_ids,
                        'total_price': total_price,
                        'purchase_date': purchase_date,
                        'warranty_until': warranty_until,
                        'plan_name': plan_name,
                        'quantity': quantity,
                        'has_warranty': has_warranty,
                        'warranty_days': warranty_days
                    })
                
                logger.debug(f"Retrieved {len(result)} purchases for user {user_id}")
                return result
                
        except Exception as e:
            logger.error(f"Failed to get user purchases: {str(e)}")
            bot_logger.log_error(e, context=f"Get purchases for user {user_id}")
            return []
    
    async def get_user_apple_ids(self, user_id: int) -> List[Dict]:
        """Get all Apple IDs purchased by a user from all sources"""
        try:
            all_apple_ids = []

            # Get from legacy purchases table
            purchases = await self.get_user_purchases(user_id)
            for purchase in purchases:
                apple_ids = purchase.get('apple_ids', [])
                purchase_date = purchase.get('purchase_date', '')
                warranty_until = purchase.get('warranty_until')
                plan_name = purchase.get('plan_name', 'نامشخص')

                for apple_id in apple_ids:
                    # Extract domain from email instead of using country
                    email = apple_id.get('Email', '')
                    domain = 'نامشخص'
                    if email:
                        from database_manager import db_manager
                        domain = db_manager.extract_domain_from_email(str(email))

                    apple_id_info = {
                        'email': email,
                        'password': apple_id.get('Password', ''),
                        'domain': domain,  # Use domain instead of country
                        'purchase_date': purchase_date,
                        'plan_name': plan_name,
                        'warranty_until': warranty_until,
                        'has_warranty': warranty_until is not None
                    }
                    all_apple_ids.append(apple_id_info)

            # Get from single_orders table
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute("""
                    SELECT so.order_id, so.domain, so.price, so.created_at, so.apple_id_assigned
                    FROM single_orders so
                    WHERE so.user_id = ? AND so.status = 'completed' AND so.apple_id_assigned IS NOT NULL
                """, (user_id,))
                single_orders = await cursor.fetchall()

                for order in single_orders:
                    order_id, domain, price, created_at, apple_id_email = order

                    # Get Apple ID details from Excel
                    from database_manager import db_manager
                    all_apple_ids_db = db_manager.get_all_apple_ids()
                    apple_id_details = None

                    for aid in all_apple_ids_db:
                        if aid.get('Email') == apple_id_email:
                            apple_id_details = aid
                            break

                    if apple_id_details:
                        apple_id_info = {
                            'email': apple_id_details.get('Email', ''),
                            'password': apple_id_details.get('Password', ''),
                            'domain': domain,
                            'purchase_date': created_at,
                            'plan_name': 'خرید تکی',
                            'warranty_until': None,
                            'has_warranty': False
                        }
                        all_apple_ids.append(apple_id_info)

                # Get from plan_orders table
                cursor = await db.execute("""
                    SELECT po.order_id, po.domain, po.price, po.created_at, po.plan_name, po.apple_ids_assigned
                    FROM plan_orders po
                    WHERE po.user_id = ? AND po.status = 'completed' AND po.apple_ids_assigned IS NOT NULL
                """, (user_id,))
                plan_orders = await cursor.fetchall()

                for order in plan_orders:
                    order_id, domain, price, created_at, plan_name, apple_ids_json = order

                    try:
                        apple_ids_list = json.loads(apple_ids_json) if apple_ids_json else []
                        for apple_id_email in apple_ids_list:
                            # Get Apple ID details from Excel
                            apple_id_details = None
                            for aid in all_apple_ids_db:
                                if aid.get('Email') == apple_id_email:
                                    apple_id_details = aid
                                    break

                            if apple_id_details:
                                apple_id_info = {
                                    'email': apple_id_details.get('Email', ''),
                                    'password': apple_id_details.get('Password', ''),
                                    'domain': domain,
                                    'purchase_date': created_at,
                                    'plan_name': plan_name,
                                    'warranty_until': None,
                                    'has_warranty': False
                                }
                                all_apple_ids.append(apple_id_info)
                    except json.JSONDecodeError:
                        continue

            logger.debug(f"Retrieved {len(all_apple_ids)} Apple IDs for user {user_id}")
            return all_apple_ids

        except Exception as e:
            logger.error(f"Failed to get user Apple IDs: {str(e)}")
            bot_logger.log_error(e, context=f"Get Apple IDs for user {user_id}")
            return []
    
    async def get_user_purchase_stats(self, user_id: int) -> Dict:
        """Get purchase statistics for a user from all sources"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # Legacy purchases
                cursor = await db.execute(
                    "SELECT COUNT(*), SUM(total_price) FROM purchases WHERE user_id = ?",
                    (user_id,)
                )
                legacy_purchases, legacy_spent = await cursor.fetchone()
                legacy_purchases = legacy_purchases or 0
                legacy_spent = legacy_spent or 0

                # Single orders
                cursor = await db.execute(
                    "SELECT COUNT(*), SUM(price) FROM single_orders WHERE user_id = ? AND status = 'completed'",
                    (user_id,)
                )
                single_purchases, single_spent = await cursor.fetchone()
                single_purchases = single_purchases or 0
                single_spent = single_spent or 0

                # Plan orders
                cursor = await db.execute(
                    "SELECT COUNT(*), SUM(price) FROM plan_orders WHERE user_id = ? AND status = 'completed'",
                    (user_id,)
                )
                plan_purchases, plan_spent = await cursor.fetchone()
                plan_purchases = plan_purchases or 0
                plan_spent = plan_spent or 0

                # Total Apple IDs from legacy purchases
                cursor = await db.execute(
                    "SELECT apple_ids FROM purchases WHERE user_id = ?",
                    (user_id,)
                )
                purchases = await cursor.fetchall()

                legacy_apple_ids = 0
                for purchase in purchases:
                    apple_ids_json = purchase[0]
                    try:
                        apple_ids = json.loads(apple_ids_json) if apple_ids_json else []
                        legacy_apple_ids += len(apple_ids)
                    except:
                        pass

                # Total Apple IDs from plan orders
                cursor = await db.execute(
                    "SELECT quantity FROM plan_orders WHERE user_id = ? AND status = 'completed'",
                    (user_id,)
                )
                plan_quantities = await cursor.fetchall()
                plan_apple_ids = sum(q[0] for q in plan_quantities if q[0])

                # Active warranties (only from legacy purchases)
                cursor = await db.execute(
                    "SELECT COUNT(*) FROM purchases WHERE user_id = ? AND warranty_until > datetime('now')",
                    (user_id,)
                )
                active_warranties = (await cursor.fetchone())[0] or 0

                # Total statistics
                total_purchases = legacy_purchases + single_purchases + plan_purchases
                total_spent = legacy_spent + single_spent + plan_spent
                total_apple_ids = legacy_apple_ids + single_purchases + plan_apple_ids  # single_purchases = single Apple IDs count

                return {
                    'total_purchases': total_purchases,
                    'total_spent': total_spent,
                    'total_apple_ids': total_apple_ids,
                    'active_warranties': active_warranties
                }

        except Exception as e:
            logger.error(f"Failed to get user purchase stats: {str(e)}")
            return {
                'total_purchases': 0,
                'total_spent': 0,
                'total_apple_ids': 0,
                'active_warranties': 0
            }
    
    def format_apple_ids_as_text(self, apple_ids: List[Dict], user_id: int) -> str:
        """Format Apple IDs as text for file export"""
        try:
            text = f"🍎 Apple ID های خریداری شده\n"
            text += f"👤 کاربر: {user_id}\n"
            text += f"📅 تاریخ تولید فایل: {datetime.now().strftime('%Y/%m/%d %H:%M')}\n"
            text += f"📊 تعداد کل: {len(apple_ids)} عدد\n"
            text += "=" * 50 + "\n\n"
            
            for i, apple_id in enumerate(apple_ids, 1):
                text += f"🔹 Apple ID #{i}\n"
                text += f"📧 Email: {apple_id.get('email', 'نامشخص')}\n"
                text += f"🔐 Password: {apple_id.get('password', 'نامشخص')}\n"
                text += f"🌐 Domain: {apple_id.get('domain', 'نامشخص')}\n"
                text += f"📦 پلن: {apple_id.get('plan_name', 'نامشخص')}\n"
                text += f"📅 تاریخ خرید: {apple_id.get('purchase_date', 'نامشخص')[:10] if apple_id.get('purchase_date') else 'نامشخص'}\n"

                # Warranty section removed as requested

                text += "-" * 30 + "\n\n"
            
            text += "⚠️ نکات مهم:\n"
            text += "• این اطلاعات محرمانه هستند\n"
            text += "• از اشتراک گذاری با دیگران خودداری کنید\n"
            text += "• در صورت مشکل با پشتیبانی تماس بگیرید\n"
            
            return text
            
        except Exception as e:
            logger.error(f"Failed to format Apple IDs as text: {str(e)}")
            return "خطا در تولید فایل"

# Global instance
user_purchases_model = UserPurchasesModel()
