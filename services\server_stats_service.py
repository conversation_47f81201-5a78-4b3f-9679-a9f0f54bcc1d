from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
import psutil
import platform
import os
import shutil
from datetime import datetime, timedelta
from logger import bot_logger, logger
from error_handler import error_handler
from utils.auth import AuthUtils
from database_manager import db_manager


class ServerStatsService:
    def __init__(self):
        self.auth_utils = AuthUtils()

    async def show_server_stats(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show server statistics and information"""
        try:
            user_id = update.effective_user.id
            
            # Only admins can view server stats
            if not await self.auth_utils.is_admin(user_id):
                await update.message.reply_text("❌ فقط ادمین‌ها می‌توانند آمار سرور را مشاهده کنند")
                return

            # Get system information
            system_info = self.get_system_info()
            
            # Get process information
            process_info = self.get_process_info()
            
            # Get disk usage
            disk_info = self.get_disk_info()
            
            # Get network information
            network_info = self.get_network_info()
            
            # Get database information
            db_info = await self.get_database_info()

            message = f"""🖥️ **آمار و اطلاعات سرور**

💻 **اطلاعات سیستم:**
• سیستم عامل: {system_info['os']}
• معماری: {system_info['architecture']}
• نام میزبان: {system_info['hostname']}
• زمان بوت: {system_info['boot_time']}
• مدت زمان فعالیت: {system_info['uptime']}

🔧 **منابع سیستم:**
• CPU: {process_info['cpu_percent']:.1f}% استفاده
• RAM: {process_info['memory_percent']:.1f}% استفاده ({process_info['memory_used']:.1f} MB از {process_info['memory_total']:.1f} MB)
• تعداد هسته‌های CPU: {system_info['cpu_cores']}
• فرکانس CPU: {system_info['cpu_freq']:.0f} MHz

💾 **فضای دیسک:**
• کل فضا: {disk_info['total']:.1f} GB
• استفاده شده: {disk_info['used']:.1f} GB ({disk_info['percent']:.1f}%)
• آزاد: {disk_info['free']:.1f} GB

🌐 **شبکه:**
• بایت ارسالی: {network_info['bytes_sent']:.1f} MB
• بایت دریافتی: {network_info['bytes_recv']:.1f} MB
• بسته‌های ارسالی: {network_info['packets_sent']:,}
• بسته‌های دریافتی: {network_info['packets_recv']:,}

🗄️ **اطلاعات دیتابیس:**
• حجم دیتابیس SQL: {db_info['sql_size']:.2f} MB
• حجم دیتابیس JSON: {db_info['json_size']:.2f} MB
• حجم فایل Excel: {db_info['excel_size']:.2f} MB
• تعداد کل جداول: {db_info['table_count']}

🐍 **اطلاعات Python:**
• نسخه Python: {system_info['python_version']}
• PID فرآیند: {process_info['pid']}
• زمان شروع ربات: {process_info['create_time']}

📊 **آمار عملکرد:**
• تعداد فایل‌های باز: {process_info['num_fds']}
• تعداد thread ها: {process_info['num_threads']}"""

            keyboard = [
                [InlineKeyboardButton("🔄 بروزرسانی", callback_data="refresh_server_stats")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)
            bot_logger.log_admin_action(user_id, "VIEW_SERVER_STATS", "Server statistics viewed")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_server_stats")

    def get_system_info(self) -> dict:
        """Get system information"""
        try:
            boot_time = datetime.fromtimestamp(psutil.boot_time())
            uptime = datetime.now() - boot_time
            
            return {
                'os': f"{platform.system()} {platform.release()}",
                'architecture': platform.machine(),
                'hostname': platform.node(),
                'python_version': platform.python_version(),
                'boot_time': boot_time.strftime('%Y-%m-%d %H:%M:%S'),
                'uptime': str(uptime).split('.')[0],  # Remove microseconds
                'cpu_cores': psutil.cpu_count(),
                'cpu_freq': psutil.cpu_freq().current if psutil.cpu_freq() else 0
            }
        except Exception as e:
            logger.error(f"Error getting system info: {str(e)}")
            return {}

    def get_process_info(self) -> dict:
        """Get current process information"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            system_memory = psutil.virtual_memory()
            
            return {
                'pid': process.pid,
                'cpu_percent': process.cpu_percent(),
                'memory_percent': process.memory_percent(),
                'memory_used': memory_info.rss / 1024 / 1024,  # Convert to MB
                'memory_total': system_memory.total / 1024 / 1024,  # Convert to MB
                'create_time': datetime.fromtimestamp(process.create_time()).strftime('%Y-%m-%d %H:%M:%S'),
                'num_fds': process.num_fds() if hasattr(process, 'num_fds') else 0,
                'num_threads': process.num_threads()
            }
        except Exception as e:
            logger.error(f"Error getting process info: {str(e)}")
            return {}

    def get_disk_info(self) -> dict:
        """Get disk usage information"""
        try:
            disk_usage = shutil.disk_usage('.')
            total = disk_usage.total / (1024**3)  # Convert to GB
            used = (disk_usage.total - disk_usage.free) / (1024**3)
            free = disk_usage.free / (1024**3)
            percent = (used / total) * 100
            
            return {
                'total': total,
                'used': used,
                'free': free,
                'percent': percent
            }
        except Exception as e:
            logger.error(f"Error getting disk info: {str(e)}")
            return {}

    def get_network_info(self) -> dict:
        """Get network statistics"""
        try:
            net_io = psutil.net_io_counters()
            
            return {
                'bytes_sent': net_io.bytes_sent / 1024 / 1024,  # Convert to MB
                'bytes_recv': net_io.bytes_recv / 1024 / 1024,  # Convert to MB
                'packets_sent': net_io.packets_sent,
                'packets_recv': net_io.packets_recv
            }
        except Exception as e:
            logger.error(f"Error getting network info: {str(e)}")
            return {}

    async def get_database_info(self) -> dict:
        """Get database file sizes and information"""
        try:
            info = {
                'sql_size': 0,
                'json_size': 0,
                'excel_size': 0,
                'table_count': 0
            }
            
            # SQL database size
            if os.path.exists(db_manager.sql_db_path):
                info['sql_size'] = os.path.getsize(db_manager.sql_db_path) / 1024 / 1024  # MB
            
            # JSON database size
            if os.path.exists(db_manager.json_db_path):
                info['json_size'] = os.path.getsize(db_manager.json_db_path) / 1024 / 1024  # MB
            
            # Excel file size
            excel_path = os.path.join('database', 'data.xlsx')
            if os.path.exists(excel_path):
                info['excel_size'] = os.path.getsize(excel_path) / 1024 / 1024  # MB
            
            # Count tables in SQL database
            import aiosqlite
            async with aiosqlite.connect(db_manager.sql_db_path) as db:
                cursor = await db.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                info['table_count'] = (await cursor.fetchone())[0]
            
            return info
            
        except Exception as e:
            logger.error(f"Error getting database info: {str(e)}")
            return {'sql_size': 0, 'json_size': 0, 'excel_size': 0, 'table_count': 0}

    async def refresh_server_stats(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Refresh server statistics"""
        try:
            await update.callback_query.answer("🔄 آمار سرور به‌روزرسانی شد", show_alert=False)
            
            # Re-show server stats with updated information
            await self.show_server_stats(update, context)
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "refresh_server_stats")


# Create service instance
server_stats_service = ServerStatsService()
