from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.error import TelegramError
from typing import Dict, List, Optional
import asyncio
import aiosqlite
import json
from datetime import datetime, timedelta
import uuid

from logger import logger, bot_logger
from error_handler import error_handler
from database_manager import db_manager
from messages import *
from services.settings_service import settings_service

class SinglePurchaseService:
    def __init__(self):
        self.db_path = db_manager.sql_db_path
        logger.info("SinglePurchaseService initialized successfully")
    
    async def show_purchase_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Show single purchase menu with available Apple IDs"""
        try:
            user_id = update.effective_user.id
            logger.debug(f"User {user_id} requested single purchase menu")

            # Get available Apple IDs
            available_ids = db_manager.get_available_apple_ids()

            if not available_ids:
                logger.info(f"No Apple IDs available for single purchase for user {user_id}")
                await update.message.reply_text(SINGLE_PURCHASE_NO_STOCK, parse_mode='Markdown')
                return
            
            # Group by email domain
            domain_groups = {}
            for apple_id in available_ids:
                email = apple_id.get('Email')

                if not email:
                    continue

                # Extract domain from email
                domain = db_manager.extract_domain_from_email(str(email))

                if domain not in domain_groups:
                    domain_groups[domain] = 0
                domain_groups[domain] += 1

            # Get domain prices from database
            domain_prices = await db_manager.get_all_domain_prices()
            price_dict = {dp['domain']: dp['price'] for dp in domain_prices if dp['is_active']}

            # Get dynamic button text and icon
            single_purchase_text = settings_service.get_button_text("single_purchase")
            single_purchase_icon = single_purchase_text.split()[0] if single_purchase_text else "🛒"
            message = f"{single_purchase_icon} **خرید تکی Apple ID**\n\n📱 **Apple ID های موجود (بر اساس دامنه):**\n\n"

            keyboard = []
            # Sort domains by count (descending)
            sorted_domains = sorted(domain_groups.items(), key=lambda x: x[1], reverse=True)

            for domain, count in sorted_domains:
                if domain and domain.strip() and domain.lower() != 'unknown':
                    # Get price for this domain
                    domain_price = price_dict.get(domain, 0)

                    if domain_price > 0:
                        price_text = f"{domain_price:,} تومان"
                        button_text = f"{domain} - {price_text}"
                        keyboard.append([InlineKeyboardButton(
                            button_text,
                            callback_data=f"single_purchase_domain_{domain}_{domain_price}"
                        )])

                        message += f"🌐 **{domain}**\n"
                        message += f"💰 قیمت: {price_text}\n"
                        message += f"📦 موجودی: {count} عدد\n\n"
                    else:
                        # Domain without price - show but disabled
                        message += f"🌐 **{domain}**\n"
                        message += f"💰 قیمت: تعریف نشده\n"
                        message += f"📦 موجودی: {count} عدد (غیرقابل خرید)\n\n"
            
            if not keyboard:
                message += "❌ هیچ Apple ID معتبری موجود نیست"
                await update.message.reply_text(message, parse_mode='Markdown')
                return
            
            message += SINGLE_PURCHASE_MENU_FOOTER

            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)
            
            bot_logger.log_user_action(
                user_id, 
                update.effective_user.username or "N/A", 
                "VIEW_SINGLE_PURCHASE_MENU", 
                f"Viewed menu with {len(available_ids)} available Apple IDs"
            )
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "show_purchase_menu")
    
    async def start_purchase_process(self, update: Update, context: ContextTypes.DEFAULT_TYPE, domain: str, price: str) -> None:
        """Start the purchase process for selected domain"""
        try:
            user_id = update.effective_user.id
            logger.debug(f"Starting purchase process for user {user_id}, domain: {domain}, price: {price}")

            # Validate price
            try:
                price_int = int(price)
            except ValueError:
                await update.callback_query.answer(SINGLE_PURCHASE_INVALID_PRICE, show_alert=True)
                return

            # Check if user has pending orders
            pending_order = await self.get_pending_order(user_id)
            logger.debug(f"Pending order check for user {user_id}: {pending_order}")
            if pending_order:
                domain_or_country = pending_order.get('domain', pending_order.get('country', 'نامشخص'))
                message = f"""❌ **نمی‌توانید سفارش جدید ثبت کنید**

📋 **سفارش در انتظار:**
• دامنه: {domain_or_country}
• قیمت: {pending_order.get('price', 0):,} تومان
• تاریخ: {pending_order.get('created_at', '')[:16]}

⏳ لطفاً منتظر تایید یا رد سفارش قبلی باشید."""

                await update.callback_query.edit_message_text(message, parse_mode='Markdown')
                return

            # Check user wallet balance
            from models.wallet import wallet_model
            user_balance = await wallet_model.get_user_balance(user_id)
            logger.debug(f"User {user_id} balance: {user_balance}, required: {price_int}")

            if user_balance < price_int:
                insufficient_message = f"""💰 **موجودی ناکافی**

💳 **موجودی فعلی:** {user_balance:,} تومان
💸 **مبلغ مورد نیاز:** {price_int:,} تومان
❌ **کمبود:** {price_int - user_balance:,} تومان

💡 **برای شارژ کیف پول خود از دکمه زیر استفاده کنید:**"""

                keyboard = [
                    [InlineKeyboardButton("💰 شارژ کیف پول", callback_data="wallet_deposit")],
                    [InlineKeyboardButton("🔙 بازگشت", callback_data="single_purchase_menu")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.callback_query.edit_message_text(
                    insufficient_message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                return

            # Check availability for the domain
            available_count = await self.get_domain_availability(domain)
            logger.debug(f"Domain {domain} availability: {available_count}")
            if available_count <= 0:
                await update.callback_query.answer(f"❌ متأسفانه اکانت {domain} موجود نیست.", show_alert=True)
                return

            # Create order ID
            order_id = str(uuid.uuid4())[:8]
            logger.debug(f"Creating order {order_id} for user {user_id}")

            # Save order data
            order_data = {
                'order_id': order_id,
                'user_id': user_id,
                'username': update.effective_user.username or "N/A",
                'first_name': update.effective_user.first_name or "N/A",
                'domain': domain,  # Use domain instead of country
                'price': price_int,
                'status': 'pending_approval',  # Changed to pending_approval
                'order_date': datetime.now().isoformat(),
                'payment_receipt': None
            }

            await self.save_order(order_data)

            logger.info(f"Single purchase order {order_id} created for user {user_id}: {domain} - {price_int:,} تومان")

            # Send order to approval group immediately
            await self.send_order_to_approval_group(context, order_data)

            message = f"""✅ **سفارش ثبت شد**

📋 **جزئیات سفارش:**
• شماره سفارش: `{order_id}`
• دامنه: {domain}
• قیمت: {price_int:,} تومان

⏳ **وضعیت:** در انتظار تایید ادمین

📤 **مرحله بعد:**
سفارش شما به ادمین‌ها ارسال شد و پس از تایید، اکانت برای شما ارسال خواهد شد.

⚠️ **نکته:** برای لغو سفارش /cancel را ارسال کنید."""

            logger.debug(f"Sending order confirmation message to user {user_id}")
            try:
                await update.callback_query.edit_message_text(message, parse_mode='Markdown')
            except Exception as edit_error:
                logger.warning(f"Could not edit message, sending new message: {edit_error}")
                await update.callback_query.message.reply_text(message, parse_mode='Markdown')

            bot_logger.log_user_action(
                user_id,
                update.effective_user.username or "N/A",
                "START_SINGLE_PURCHASE",
                f"Order {order_id}: {domain} - {price_int:,} تومان"
            )
            
        except Exception as e:
            logger.error(f"Exception in start_purchase_process: {str(e)}")
            await error_handler.handle_error(update, context, e, "start_purchase_process")
    
    async def process_payment_receipt(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Process payment receipt from user"""
        try:
            user_id = update.effective_user.id
            order_data = context.user_data.get('current_order')
            
            if not order_data:
                await update.message.reply_text("❌ سفارش فعالی یافت نشد. لطفاً مجدداً سفارش دهید.")
                return
            
            # Check if message has photo
            if not update.message.photo:
                await update.message.reply_text(SINGLE_PURCHASE_NO_RECEIPT)
                return
            
            # Get the largest photo
            photo = update.message.photo[-1]
            file_id = photo.file_id
            
            # Update order with receipt
            order_data['payment_receipt'] = file_id
            order_data['status'] = 'pending_approval'
            order_data['receipt_date'] = datetime.now().isoformat()
            
            await self.update_order(order_data)
            
            # Send to approval group
            await self.send_to_approval_group(context, order_data)
            
            # Clear user data
            context.user_data.pop('current_order', None)
            context.user_data.pop('purchase_step', None)

            message = SINGLE_PURCHASE_RECEIPT_RECEIVED.format(
                order_id=order_data['order_id'],
                country=order_data.get('domain', order_data.get('country', 'نامشخص')),
                price=order_data['price']
            )

            await update.message.reply_text(message, parse_mode='Markdown')
            
            bot_logger.log_user_action(
                user_id, 
                update.effective_user.username or "N/A", 
                "PAYMENT_RECEIPT_SENT", 
                f"Order {order_data['order_id']}: Receipt uploaded"
            )
            
        except Exception as e:
            await error_handler.handle_error(update, context, e, "process_payment_receipt")

    async def get_pending_order(self, user_id: int) -> Optional[Dict]:
        """Get user's pending order"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # Check if table exists first
                cursor = await db.execute(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name='single_orders'"
                )
                table_exists = await cursor.fetchone()

                if not table_exists:
                    logger.debug("single_orders table does not exist, returning None")
                    return None

                cursor = await db.execute(
                    "SELECT * FROM single_orders WHERE user_id = ? AND status IN ('pending', 'pending_approval') ORDER BY rowid DESC LIMIT 1",
                    (user_id,)
                )
                row = await cursor.fetchone()

                if row:
                    columns = [description[0] for description in cursor.description]
                    return dict(zip(columns, row))
                return None

        except Exception as e:
            logger.error(f"Error getting pending order: {str(e)}")
            return None

    async def get_domain_availability(self, domain: str) -> int:
        """Get available count for specific domain"""
        try:
            from database_manager import db_manager
            apple_ids = db_manager.get_available_apple_ids()

            logger.debug(f"Total available Apple IDs: {len(apple_ids)}")
            logger.debug(f"Looking for domain: {domain}")

            # Count available Apple IDs for this domain
            count = 0
            sample_emails = []
            sample_domains = []
            for apple_id in apple_ids:
                email = apple_id.get('Email', '')  # Use 'Email' with capital E
                extracted_domain = db_manager.extract_domain_from_email(str(email))

                if len(sample_emails) < 5:  # Log first 5 emails for debugging
                    sample_emails.append(email)
                    sample_domains.append(extracted_domain)

                if extracted_domain == domain:
                    count += 1

            logger.debug(f"Sample emails: {sample_emails}")
            logger.debug(f"Sample extracted domains: {sample_domains}")
            logger.debug(f"Found {count} Apple IDs for domain {domain}")

            return count

        except Exception as e:
            logger.error(f"Error getting domain availability: {str(e)}")
            return 0

    async def save_order(self, order_data: Dict) -> bool:
        """Save order to database"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    INSERT INTO single_orders
                    (order_id, user_id, username, first_name, domain, price, status, order_date, payment_receipt)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    order_data['order_id'],
                    order_data['user_id'],
                    order_data['username'],
                    order_data['first_name'],
                    order_data.get('domain', order_data.get('country', 'نامشخص')),
                    order_data['price'],
                    order_data['status'],
                    order_data['order_date'],
                    order_data.get('payment_receipt')
                ))

                await db.commit()
                logger.info(f"Order {order_data['order_id']} saved successfully")
                return True

        except Exception as e:
            logger.error(f"Error saving order: {str(e)}")
            return False

    async def update_order(self, order_data: Dict) -> bool:
        """Update order in database"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    UPDATE single_orders
                    SET status = ?, payment_receipt = ?, receipt_date = ?
                    WHERE order_id = ?
                """, (
                    order_data['status'],
                    order_data.get('payment_receipt'),
                    order_data.get('receipt_date'),
                    order_data['order_id']
                ))

                await db.commit()
                logger.info(f"Order {order_data['order_id']} updated successfully")
                return True

        except Exception as e:
            logger.error(f"Error updating order: {str(e)}")
            return False

    async def get_bank_info(self) -> str:
        """Get bank information for payment"""
        try:
            import os

            # Get bank info from environment variables
            bank_name = os.getenv('BANK_NAME', 'نامشخص')
            account_number = os.getenv('BANK_ACCOUNT_NUMBER', 'نامشخص')
            account_holder = os.getenv('BANK_ACCOUNT_HOLDER', 'نامشخص')
            # iban = os.getenv('BANK_IBAN', 'نامشخص')  # Temporarily disabled

            # Check if bank info is configured
            if bank_name == 'نامشخص' or account_number == 'نامشخص' or account_holder == 'نامشخص':
                return SINGLE_PURCHASE_BANK_INFO_UPDATING

            # Format bank info without IBAN for now
            bank_info = f"""💳 **اطلاعات بانکی:**
🏦 نام بانک: {bank_name}
💳 شماره حساب: {account_number}
👤 نام صاحب حساب: {account_holder}"""

            return bank_info

        except Exception as e:
            logger.error(f"Error getting bank info: {str(e)}")
            return SINGLE_PURCHASE_BANK_INFO_ERROR

    async def send_to_approval_group(self, context: ContextTypes.DEFAULT_TYPE, order_data: Dict) -> None:
        """Send order to approval group"""
        try:
            import os

            # Get approval group ID from environment variables
            approval_group_id = os.getenv('PAYMENT_GROUP_ID')

            if not approval_group_id:
                logger.warning("Payment group ID not set in environment variables")
                return

            message = SINGLE_PURCHASE_APPROVAL_GROUP_MESSAGE.format(
                first_name=order_data['first_name'],
                username=order_data['username'],
                user_id=order_data['user_id'],
                order_id=order_data['order_id'],
                country=order_data.get('domain', order_data.get('country', 'نامشخص')),
                price=order_data['price'],
                order_date=order_data['order_date'][:16]
            )

            keyboard = [
                [
                    InlineKeyboardButton("✅ تایید", callback_data=f"approve_order_{order_data['order_id']}"),
                    InlineKeyboardButton("❌ رد", callback_data=f"reject_order_{order_data['order_id']}")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Send message with receipt photo
            await context.bot.send_photo(
                chat_id=approval_group_id,
                photo=order_data['payment_receipt'],
                caption=message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            logger.info(f"Order {order_data['order_id']} sent to approval group")

        except Exception as e:
            logger.error(f"Error sending to approval group: {str(e)}")

    async def cancel_purchase(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Cancel current purchase"""
        try:
            user_id = update.effective_user.id
            order_data = context.user_data.get('current_order')

            if order_data:
                # Update order status to cancelled
                order_data['status'] = 'cancelled'
                await self.update_order_status(order_data['order_id'], 'cancelled')

            # Clear user data
            context.user_data.pop('current_order', None)
            context.user_data.pop('purchase_step', None)

            message = SINGLE_PURCHASE_CANCELLED

            if hasattr(update, 'callback_query') and update.callback_query:
                await update.callback_query.edit_message_text(message, parse_mode='Markdown')
            else:
                await update.message.reply_text(message, parse_mode='Markdown')

            bot_logger.log_user_action(
                user_id,
                update.effective_user.username or "N/A",
                "SINGLE_PURCHASE_CANCELLED",
                "Purchase cancelled by user"
            )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "cancel_purchase")

    async def update_order_status(self, order_id: str, status: str) -> bool:
        """Update order status"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute(
                    "UPDATE single_orders SET status = ? WHERE order_id = ?",
                    (status, order_id)
                )
                await db.commit()
                return True

        except Exception as e:
            logger.error(f"Error updating order status: {str(e)}")
            return False

    async def approve_order(self, update: Update, context: ContextTypes.DEFAULT_TYPE, order_id: str) -> None:
        """Approve order and send Apple ID to user"""
        try:
            # Get order details
            order = await self.get_order_by_id(order_id)
            if not order:
                await update.callback_query.answer("❌ سفارش یافت نشد", show_alert=True)
                return

            if order['status'] != 'pending_approval':
                await update.callback_query.answer("❌ سفارش قابل تایید نیست", show_alert=True)
                return

            # Get available Apple ID for the domain
            available_ids = db_manager.get_available_apple_ids()
            suitable_id = None

            # Look for Apple ID with matching domain
            target_domain = order.get('domain', order.get('country', ''))  # Support both old and new format

            for apple_id in available_ids:
                email = apple_id.get('Email', '')
                if email:
                    apple_domain = db_manager.extract_domain_from_email(str(email))
                    if apple_domain.lower() == target_domain.lower():
                        suitable_id = apple_id
                        break

            if not suitable_id:
                # If no exact domain match, get any available Apple ID
                suitable_id = available_ids[0] if available_ids else None

            if not suitable_id:
                await update.callback_query.answer("❌ Apple ID موجود نیست", show_alert=True)
                return

            # Prepare buyer information
            buyer_info = {
                'user_id': order['user_id'],
                'username': order['username'],
                'first_name': order['first_name'],
                'last_name': order['last_name']
            }

            # Update Apple ID status to sold with buyer information
            success = db_manager.update_apple_id_status(suitable_id['ID'], 'sold', buyer_info)
            if not success:
                logger.error(f"Failed to update Apple ID {suitable_id['ID']} status to sold for order {order_id}")
                await update.callback_query.answer("❌ خطا در بروزرسانی وضعیت", show_alert=True)
                return

            # Update order status
            await self.update_order_status(order_id, 'approved')
            await self.update_order_apple_id(order_id, suitable_id['ID'])

            logger.info(f"Single purchase order {order_id} approved - Apple ID {suitable_id['Email']} assigned to user {order['user_id']}")

            # Send Apple ID to user
            await self.send_apple_id_to_user(context, order, suitable_id)

            # Update approval message
            updated_message = f"""✅ **سفارش تایید شد**

👤 **کاربر:** {order['first_name']} (@{order['username']})
📋 **سفارش:** `{order_id}`
🌍 **دامنه:** {order.get('domain', order.get('country', 'نامشخص'))}
💰 **قیمت:** {order['price']:,} تومان

📱 **Apple ID ارسال شده:** {suitable_id['Email']}
⏰ **تاریخ تایید:** {datetime.now().strftime('%Y/%m/%d %H:%M')}

✅ **وضعیت:** تکمیل شده"""

            await update.callback_query.edit_message_caption(
                caption=updated_message,
                parse_mode='Markdown'
            )

            bot_logger.log_admin_action(
                update.effective_user.id,
                "APPROVE_SINGLE_ORDER",
                f"Order {order_id} approved - Apple ID {suitable_id['Email']} sent to user {order['user_id']}"
            )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "approve_order")

    async def reject_order(self, update: Update, context: ContextTypes.DEFAULT_TYPE, order_id: str) -> None:
        """Reject order"""
        try:
            # Get order details
            order = await self.get_order_by_id(order_id)
            if not order:
                await update.callback_query.answer("❌ سفارش یافت نشد", show_alert=True)
                return

            if order['status'] != 'pending_approval':
                await update.callback_query.answer("❌ سفارش قابل رد نیست", show_alert=True)
                return

            # Update order status
            await self.update_order_status(order_id, 'rejected')

            logger.info(f"Single purchase order {order_id} rejected for user {order['user_id']}")

            # Send rejection message to user
            await self.send_rejection_to_user(context, order)

            # Update approval message
            updated_message = f"""❌ **سفارش رد شد**

👤 **کاربر:** {order['first_name']} (@{order['username']})
📋 **سفارش:** `{order_id}`
🌍 **دامنه:** {order.get('domain', order.get('country', 'نامشخص'))}
💰 **قیمت:** {order['price']:,} تومان

⏰ **تاریخ رد:** {datetime.now().strftime('%Y/%m/%d %H:%M')}

❌ **وضعیت:** رد شده"""

            await update.callback_query.edit_message_caption(
                caption=updated_message,
                parse_mode='Markdown'
            )

            bot_logger.log_admin_action(
                update.effective_user.id,
                "REJECT_SINGLE_ORDER",
                f"Order {order_id} rejected for user {order['user_id']}"
            )

        except Exception as e:
            await error_handler.handle_error(update, context, e, "reject_order")

    async def get_order_by_id(self, order_id: str) -> Optional[Dict]:
        """Get order by ID"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute(
                    "SELECT * FROM single_orders WHERE order_id = ?",
                    (order_id,)
                )
                row = await cursor.fetchone()

                if row:
                    columns = [description[0] for description in cursor.description]
                    return dict(zip(columns, row))
                return None

        except Exception as e:
            logger.error(f"Error getting order by ID: {str(e)}")
            return None

    async def update_order_apple_id(self, order_id: str, apple_id: str) -> bool:
        """Update order with assigned Apple ID"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute(
                    "UPDATE single_orders SET apple_id_assigned = ? WHERE order_id = ?",
                    (apple_id, order_id)
                )
                await db.commit()
                return True

        except Exception as e:
            logger.error(f"Error updating order Apple ID: {str(e)}")
            return False

    async def send_apple_id_to_user(self, context: ContextTypes.DEFAULT_TYPE, order: Dict, apple_id: Dict) -> None:
        """Send Apple ID details to user"""
        try:
            # Extract domain from email
            email = apple_id.get('Email', '')
            domain = 'نامشخص'
            if email:
                domain = db_manager.extract_domain_from_email(str(email))

            # Use domain instead of country
            domain_or_country = order.get('domain', order.get('country', domain))

            message = SINGLE_PURCHASE_APPROVED.format(
                order_id=order['order_id'],
                country=domain_or_country,  # This will show domain instead of country
                price=order['price'],
                email=apple_id['Email'],
                password=apple_id['Password'],
                q1=apple_id['Q1'],
                q2=apple_id['Q2'],
                q3=apple_id['Q3'],
                date=apple_id['Date']
            )

            await context.bot.send_message(
                chat_id=order['user_id'],
                text=message,
                parse_mode='Markdown'
            )

            logger.info(f"Apple ID {apple_id['Email']} sent to user {order['user_id']}")

        except Exception as e:
            logger.error(f"Error sending Apple ID to user: {str(e)}")

    async def send_rejection_to_user(self, context: ContextTypes.DEFAULT_TYPE, order: Dict) -> None:
        """Send rejection message to user"""
        try:
            message = SINGLE_PURCHASE_REJECTED.format(
                order_id=order['order_id'],
                country=order.get('domain', order.get('country', 'نامشخص')),
                price=order['price']
            )

            await context.bot.send_message(
                chat_id=order['user_id'],
                text=message,
                parse_mode='Markdown'
            )

            logger.info(f"Rejection message sent to user {order['user_id']} for order {order['order_id']}")

        except Exception as e:
            logger.error(f"Error sending rejection to user: {str(e)}")

    async def send_order_to_approval_group(self, context: ContextTypes.DEFAULT_TYPE, order_data: Dict) -> None:
        """Send order to approval group for immediate approval"""
        try:
            import os

            # Get approval group ID from environment variables
            approval_group_id = os.getenv('PAYMENT_GROUP_ID')

            if not approval_group_id:
                logger.warning("Payment group ID not set in environment variables")
                return

            user_info = f"👤 **کاربر:** {order_data['first_name']}"
            if order_data['username'] != "N/A":
                user_info += f" (@{order_data['username']})"
            user_info += f"\n🆔 **ID:** `{order_data['user_id']}`"

            message = f"""🛒 **درخواست خرید تکی جدید**

{user_info}

📋 **جزئیات سفارش:**
• شماره سفارش: `{order_data['order_id']}`
• دامنه: {order_data['domain']}
• قیمت: {order_data['price']:,} تومان
• تاریخ: {order_data['order_date'][:16]}

⏳ **وضعیت:** در انتظار تایید"""

            keyboard = [
                [
                    InlineKeyboardButton("✅ تایید", callback_data=f"approve_single_order_{order_data['order_id']}"),
                    InlineKeyboardButton("❌ رد", callback_data=f"reject_single_order_{order_data['order_id']}")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Send message to approval group
            sent_message = await context.bot.send_message(
                chat_id=approval_group_id,
                text=message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            # Store group message ID in order data for later updates
            await self.update_order_group_message_id(order_data['order_id'], sent_message.message_id)

            logger.info(f"Order {order_data['order_id']} sent to approval group {approval_group_id}")

        except Exception as e:
            logger.error(f"Error sending order to approval group: {str(e)}")

    async def update_order_group_message_id(self, order_id: str, message_id: int) -> None:
        """Update order with group message ID for later reference"""
        try:
            import aiosqlite
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute(
                    "UPDATE single_orders SET group_message_id = ? WHERE order_id = ?",
                    (message_id, order_id)
                )
                await db.commit()
                logger.debug(f"Updated order {order_id} with group message ID {message_id}")
        except Exception as e:
            logger.error(f"Error updating order group message ID: {str(e)}")

    async def approve_single_order(self, update: Update, context: ContextTypes.DEFAULT_TYPE, order_id: str) -> None:
        """Approve single order and send Apple ID to user"""
        try:
            logger.debug(f"Admin {update.effective_user.id} attempting to approve order {order_id}")
            # Get order details
            order = await self.get_order_by_id(order_id)
            logger.debug(f"Order details for {order_id}: {order}")
            if not order:
                await update.callback_query.answer("❌ سفارش یافت نشد.", show_alert=True)
                return

            if order['status'] != 'pending_approval':
                await update.callback_query.answer("❌ این سفارش قبلاً پردازش شده است.", show_alert=True)
                return

            # Get available Apple ID for the domain
            apple_id = await self.get_apple_id_for_domain(order['domain'])
            if not apple_id:
                await update.callback_query.answer(f"❌ اکانت {order['domain']} موجود نیست.", show_alert=True)
                return

            # Update order status and assign Apple ID
            await self.update_order_status(order_id, 'completed')
            await self.update_order_apple_id(order_id, apple_id['Email'])

            # Deduct amount from user wallet
            from models.wallet import wallet_model
            await wallet_model.create_purchase_transaction(
                order['user_id'],
                order['price'],
                f"خرید تکی Apple ID - سفارش {order_id}"
            )

            # Mark Apple ID as sold with buyer information
            from database_manager import db_manager
            buyer_info = {
                'user_id': order['user_id'],
                'username': order['username'],
                'first_name': order['first_name'],
                'last_name': order.get('last_name', '')
            }
            await db_manager.mark_apple_id_as_sold(apple_id['Email'], buyer_info)

            # Send Apple ID to user
            await self.send_apple_id_to_user(context, order, apple_id)

            # Don't send separate notification - just update the original message

            # Update callback message
            admin_name = update.effective_user.first_name or "ادمین"
            new_message = f"""✅ **سفارش تایید شد**

👤 **کاربر:** {order['first_name']} (@{order['username']})
🆔 **ID:** `{order['user_id']}`

📋 **جزئیات سفارش:**
• شماره سفارش: `{order['order_id']}`
• دامنه: {order['domain']}
• قیمت: {order['price']:,} تومان

✅ **تایید شده توسط:** {admin_name}
📧 **اکانت ارسال شده:** {apple_id['Email']}
⏰ **تاریخ تایید:** {datetime.now().strftime('%Y/%m/%d %H:%M')}"""

            await update.callback_query.edit_message_text(new_message, parse_mode='Markdown')
            await update.callback_query.answer("✅ سفارش تایید شد و اکانت ارسال شد.", show_alert=True)

            logger.info(f"Single order {order_id} approved by admin {update.effective_user.id}")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "approve_single_order")

    async def reject_single_order(self, update: Update, context: ContextTypes.DEFAULT_TYPE, order_id: str) -> None:
        """Reject single order"""
        try:
            logger.debug(f"Admin {update.effective_user.id} attempting to reject order {order_id}")
            # Get order details
            order = await self.get_order_by_id(order_id)
            logger.debug(f"Order details for {order_id}: {order}")
            if not order:
                await update.callback_query.answer("❌ سفارش یافت نشد.", show_alert=True)
                return

            if order['status'] != 'pending_approval':
                await update.callback_query.answer("❌ این سفارش قبلاً پردازش شده است.", show_alert=True)
                return

            # Update order status
            await self.update_order_status(order_id, 'rejected')

            # Send rejection message to user
            await self.send_rejection_to_user(context, order)

            # Don't send separate notification - just update the original message

            # Update callback message
            admin_name = update.effective_user.first_name or "ادمین"
            new_message = f"""❌ **سفارش رد شد**

👤 **کاربر:** {order['first_name']} (@{order['username']})
🆔 **ID:** `{order['user_id']}`

📋 **جزئیات سفارش:**
• شماره سفارش: `{order['order_id']}`
• دامنه: {order['domain']}
• قیمت: {order['price']:,} تومان

❌ **رد شده توسط:** {admin_name}
⏰ **تاریخ رد:** {datetime.now().strftime('%Y/%m/%d %H:%M')}"""

            await update.callback_query.edit_message_text(new_message, parse_mode='Markdown')
            await update.callback_query.answer("❌ سفارش رد شد.", show_alert=True)

            logger.info(f"Single order {order_id} rejected by admin {update.effective_user.id}")

        except Exception as e:
            await error_handler.handle_error(update, context, e, "reject_single_order")

    async def cancel_pending_orders(self, user_id: int) -> None:
        """Cancel all pending orders for a user"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # Check if table exists first
                cursor = await db.execute(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name='single_orders'"
                )
                table_exists = await cursor.fetchone()

                if not table_exists:
                    return

                # Get pending orders before cancelling
                cursor = await db.execute(
                    "SELECT * FROM single_orders WHERE user_id = ? AND status IN ('pending', 'pending_approval')",
                    (user_id,)
                )
                pending_orders = await cursor.fetchall()

                if pending_orders:
                    columns = [description[0] for description in cursor.description]
                    orders = [dict(zip(columns, row)) for row in pending_orders]

                    # Update all pending orders to cancelled
                    await db.execute(
                        "UPDATE single_orders SET status = 'cancelled' WHERE user_id = ? AND status IN ('pending', 'pending_approval')",
                        (user_id,)
                    )
                    await db.commit()

                    # Update group messages for cancelled orders
                    for order in orders:
                        await self.update_group_message_for_cancelled_order(order)

                    logger.info(f"Cancelled {len(orders)} pending single orders for user {user_id}")

        except Exception as e:
            logger.error(f"Error cancelling pending orders for user {user_id}: {str(e)}")

    async def update_group_message_for_cancelled_order(self, order: Dict) -> None:
        """Update group message when order is cancelled by user"""
        try:
            import os
            from telegram import Bot

            # Get payment group ID
            payment_group_id = os.getenv('PAYMENT_GROUP_ID')
            if not payment_group_id:
                logger.warning("Payment group ID not configured")
                return

            # Get group message ID from order
            group_message_id = order.get('group_message_id')
            if not group_message_id:
                logger.warning(f"No group message ID found for order {order['order_id']}")
                return

            # Create bot instance
            bot_token = os.getenv('BOT_TOKEN')
            if not bot_token:
                logger.error("Bot token not found")
                return

            bot = Bot(token=bot_token)

            # Create cancelled message
            cancelled_message = f"""❌ **سفارش لغو شد**

👤 **کاربر:** {order['first_name']} (@{order['username']})
🆔 **ID:** `{order['user_id']}`

📋 **جزئیات سفارش:**
• شماره سفارش: `{order['order_id']}`
• دامنه: {order.get('domain', order.get('country', 'نامشخص'))}
• قیمت: {order['price']:,} تومان
• تاریخ سفارش: {order['created_at'][:16]}

❌ **وضعیت:** لغو شده توسط کاربر
⏰ **زمان لغو:** {datetime.now().strftime('%Y/%m/%d %H:%M')}"""

            # Update the group message
            await bot.edit_message_text(
                chat_id=payment_group_id,
                message_id=group_message_id,
                text=cancelled_message,
                parse_mode='Markdown'
            )

            logger.info(f"Updated group message for cancelled order {order['order_id']}")

        except Exception as e:
            logger.error(f"Error updating group message for cancelled order: {str(e)}")



    async def get_apple_id_for_domain(self, domain: str) -> Optional[Dict]:
        """Get available Apple ID for specific domain"""
        try:
            from database_manager import db_manager
            apple_ids = db_manager.get_available_apple_ids()

            # Find first available Apple ID for this domain
            for apple_id in apple_ids:
                email = apple_id.get('Email', '')  # Use 'Email' with capital E
                if email and email.endswith(f'@{domain}'):
                    return apple_id

            return None

        except Exception as e:
            logger.error(f"Error getting Apple ID for domain: {str(e)}")
            return None

    async def send_apple_id_to_user(self, context: ContextTypes.DEFAULT_TYPE, order: Dict, apple_id: Dict) -> None:
        """Send Apple ID to user in a clean format"""
        try:
            message = f"""✅ **سفارش شما تایید شد!**

📋 **جزئیات سفارش:**
• شماره سفارش: `{order['order_id']}`
• دامنه: {order['domain']}
• قیمت: {order['price']:,} تومان

📧 **اطلاعات اکانت:**
• ایمیل: `{apple_id['Email']}`
• پسورد: `{apple_id['Password']}`

🔐 **سوالات امنیتی:**
• سوال 1: `{apple_id.get('Q1', 'N/A')}`
• سوال 2: `{apple_id.get('Q2', 'N/A')}`
• سوال 3: `{apple_id.get('Q3', 'N/A')}`

📅 **تاریخ ایجاد:** `{apple_id.get('Date', 'N/A')}`

🎉 **تبریک!** اکانت Apple ID شما آماده استفاده است.

⚠️ **نکات مهم:**
• اطلاعات را در جای امن نگهداری کنید
• از تغییر اطلاعات حساب خودداری کنید
• در صورت مشکل با پشتیبانی تماس بگیرید"""

            await context.bot.send_message(
                chat_id=order['user_id'],
                text=message,
                parse_mode='Markdown'
            )

            logger.info(f"Apple ID sent to user {order['user_id']} for order {order['order_id']}")

        except Exception as e:
            logger.error(f"Error sending Apple ID to user: {str(e)}")

    async def send_rejection_to_user(self, context: ContextTypes.DEFAULT_TYPE, order: Dict) -> None:
        """Send rejection message to user"""
        try:
            message = f"""❌ **سفارش شما رد شد**

📋 **جزئیات سفارش:**
• شماره سفارش: `{order['order_id']}`
• دامنه: {order['domain']}
• قیمت: {order['price']:,} تومان

😔 **متأسفانه** سفارش شما توسط ادمین رد شد.

💬 **راه‌های ارتباط:**
• برای اطلاع از دلیل رد سفارش با پشتیبانی تماس بگیرید
• می‌توانید سفارش جدید ثبت کنید"""

            await context.bot.send_message(
                chat_id=order['user_id'],
                text=message,
                parse_mode='Markdown'
            )

            logger.info(f"Rejection message sent to user {order['user_id']} for order {order['order_id']}")

        except Exception as e:
            logger.error(f"Error sending rejection to user: {str(e)}")

    async def get_all_pending_orders(self) -> List[Dict]:
        """Get all pending single orders"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute(
                    "SELECT * FROM single_orders WHERE status = 'pending_approval' ORDER BY created_at DESC"
                )
                rows = await cursor.fetchall()

                if rows:
                    columns = [description[0] for description in cursor.description]
                    return [dict(zip(columns, row)) for row in rows]
                return []

        except Exception as e:
            logger.error(f"Error getting all pending orders: {str(e)}")
            return []

    async def send_single_approval_to_group(self, context: ContextTypes.DEFAULT_TYPE, order: Dict, apple_id: Dict, admin_name: str) -> None:
        """Send single order approval notification to group"""
        try:
            import os

            # Get approval group ID from environment variables
            group_id = os.getenv('PAYMENT_GROUP_ID')

            if not group_id:
                logger.warning("Payment group ID not set in environment variables")
                return

            user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
            if not user_name:
                user_name = "نام نامشخص"

            username = f"@{order['username']}" if order.get('username') else "بدون نام کاربری"

            message = f"""✅ **سفارش تکی تایید شد**

👤 **کاربر:** {user_name}
🆔 **نام کاربری:** {username}
📱 **شناسه تلگرام:** `{order['user_id']}`

📋 **جزئیات سفارش:**
• شماره سفارش: `{order['order_id']}`
• دامنه: {order['domain']}
• قیمت: {order['price']:,} تومان

✅ **تایید شده توسط:** {admin_name}
📧 **اکانت تحویل داده شد:** {apple_id['Email']}

⏰ **زمان:** {datetime.now().strftime('%Y/%m/%d %H:%M:%S')}"""

            await context.bot.send_message(
                chat_id=group_id,
                text=message,
                parse_mode='Markdown'
            )

            logger.info(f"Single order approval notification sent to group for order {order['order_id']}")

        except Exception as e:
            logger.error(f"Error sending single approval to group: {str(e)}")

    async def send_single_rejection_to_group(self, context: ContextTypes.DEFAULT_TYPE, order: Dict, admin_name: str) -> None:
        """Send single order rejection notification to group"""
        try:
            import os

            # Get approval group ID from environment variables
            group_id = os.getenv('PAYMENT_GROUP_ID')

            if not group_id:
                logger.warning("Payment group ID not set in environment variables")
                return

            user_name = f"{order.get('first_name', '')} {order.get('last_name', '')}".strip()
            if not user_name:
                user_name = "نام نامشخص"

            username = f"@{order['username']}" if order.get('username') else "بدون نام کاربری"

            message = f"""❌ **سفارش تکی رد شد**

👤 **کاربر:** {user_name}
🆔 **نام کاربری:** {username}
📱 **شناسه تلگرام:** `{order['user_id']}`

📋 **جزئیات سفارش:**
• شماره سفارش: `{order['order_id']}`
• دامنه: {order['domain']}
• قیمت: {order['price']:,} تومان

❌ **رد شده توسط:** {admin_name}

⏰ **زمان:** {datetime.now().strftime('%Y/%m/%d %H:%M:%S')}"""

            await context.bot.send_message(
                chat_id=group_id,
                text=message,
                parse_mode='Markdown'
            )

            logger.info(f"Single order rejection notification sent to group for order {order['order_id']}")

        except Exception as e:
            logger.error(f"Error sending single rejection to group: {str(e)}")

# Global instance
single_purchase_service = SinglePurchaseService()
